# Local Store Module PRD

## Overview
The Local Store module provides a unified data persistence layer for all Productivity Guard components, handling screenshots, logs, task data, and analysis results with privacy-first local storage.

## Requirements Mapping

| PRD Requirement | Description | Implementation |
|----------------|-------------|----------------|
| **F-1** | Persist task list to local store | `LocalStore.save_tasks()` |
| **F-5** | Persist screenshots to dated folders | `LocalStore.save_screenshot()` |
| **F-13** | Store screenshots and logs locally only | All storage methods local-only |
| **F-14** | Offer redaction module for sensitive regions | `LocalStore.redact_screenshot()` |
| **N-2** | Modular with TypeHinted interfaces | All methods with type hints, passes mypy --strict |
| **N-3** | Configurable paths and settings | `StoreConfig` dataclass |
| **N-5** | No outbound network calls | No network dependencies |

## Implementation Approach

```python
# ── /src/local_store/config.py ──────────────────────────────────────────────────
from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Final

__all__: Final = ["StoreConfig"]

@dataclass(slots=True)
class StoreConfig:
    """
    Configuration for local storage.
    
    Requirement: N-3
    Version: v01
    """
    base_path: Path = Path("./data")
    screenshots_path: Path = Path("./output/screenshots")
    logs_path: Path = Path("./output/logs")
    tasks_file: str = "tasks.json"
    analysis_file: str = "analysis_log.jsonl"
    redaction_enabled: bool = False
    max_storage_days: int = 30  # Auto-cleanup after N days
    compression_enabled: bool = True
```

```python
# ── /src/local_store/models.py ──────────────────────────────────────────────────
from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Optional

__all__ = ["StorageType", "StorageRecord", "RedactionRegion"]

class StorageType(Enum):
    """Type of data being stored."""
    SCREENSHOT = "screenshot"
    TASK_DATA = "task_data"
    ANALYSIS_LOG = "analysis_log"
    ESCALATION_LOG = "escalation_log"
    SYSTEM_LOG = "system_log"

@dataclass
class StorageRecord:
    """
    Record of stored data.
    
    Requirement: F-13
    Version: v01
    """
    id: str
    storage_type: StorageType
    file_path: Path
    timestamp: datetime
    size_bytes: int
    metadata: dict[str, Any]
    is_redacted: bool = False
    compression_ratio: Optional[float] = None

@dataclass
class RedactionRegion:
    """
    Region to redact in screenshots.
    
    Requirement: F-14
    Version: v01
    """
    x: int
    y: int
    width: int
    height: int
    blur_intensity: int = 10
    reason: str = "sensitive_content"
```

```python
# ── /src/local_store/redactor.py ────────────────────────────────────────────────
from __future__ import annotations

import logging
from pathlib import Path
from typing import Optional

from PIL import Image, ImageFilter

from .models import RedactionRegion

__all__ = ["Redactor"]

_LOG = logging.getLogger(__name__)

class Redactor:
    """
    Handle screenshot redaction for privacy.
    
    Requirement: F-14
    Version: v01
    """
    
    def __init__(self, enabled: bool = True) -> None:
        """
        Initialize redactor.
        
        Requirement: F-14
        Version: v01
        """
        self.enabled = enabled
        self.default_regions: list[RedactionRegion] = []
    
    def add_default_region(self, region: RedactionRegion) -> None:
        """Add a region to always redact."""
        self.default_regions.append(region)
        _LOG.info(f"Added default redaction region: {region.reason}")
    
    def redact_screenshot(
        self,
        image_path: Path,
        regions: Optional[list[RedactionRegion]] = None,
        output_path: Optional[Path] = None
    ) -> Path:
        """
        Redact sensitive regions in screenshot.
        
        Requirement: F-14
        Version: v01
        """
        if not self.enabled:
            return image_path
        
        try:
            # Load image
            img = Image.open(image_path)
            
            # Combine custom and default regions
            all_regions = (regions or []) + self.default_regions
            
            if not all_regions:
                return image_path
            
            # Apply redaction to each region
            for region in all_regions:
                self._blur_region(img, region)
            
            # Save redacted image
            if output_path is None:
                output_path = image_path.parent / f"{image_path.stem}_redacted{image_path.suffix}"
            
            img.save(output_path)
            _LOG.info(f"Redacted {len(all_regions)} regions in {image_path}")
            
            return output_path
            
        except Exception as e:
            _LOG.error(f"Failed to redact {image_path}: {e}")
            return image_path
    
    def _blur_region(self, img: Image.Image, region: RedactionRegion) -> None:
        """Apply blur to specific region."""
        # Extract region
        box = (
            region.x,
            region.y,
            region.x + region.width,
            region.y + region.height
        )
        
        # Ensure box is within image bounds
        box = (
            max(0, box[0]),
            max(0, box[1]),
            min(img.width, box[2]),
            min(img.height, box[3])
        )
        
        if box[2] <= box[0] or box[3] <= box[1]:
            return
        
        # Extract, blur, and paste back
        region_img = img.crop(box)
        blurred = region_img.filter(
            ImageFilter.GaussianBlur(radius=region.blur_intensity)
        )
        img.paste(blurred, box)
    
    def detect_sensitive_regions(self, image_path: Path) -> list[RedactionRegion]:
        """
        Auto-detect potentially sensitive regions.
        
        Requirement: F-14
        Version: v01
        """
        # Placeholder for ML-based detection
        # In production: use OCR + pattern matching for:
        # - Credit card numbers
        # - SSNs
        # - Email addresses
        # - Phone numbers
        # - API keys
        
        regions = []
        
        # For now, return empty list
        # Future: implement actual detection
        
        return regions
```

```python
# ── /src/local_store/store.py ───────────────────────────────────────────────────
from __future__ import annotations

import gzip
import json
import logging
import shutil
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Optional

from .config import StoreConfig
from .models import StorageRecord, StorageType, RedactionRegion
from .redactor import Redactor

__all__ = ["LocalStore"]

_LOG = logging.getLogger(__name__)

class LocalStore:
    """
    Unified local storage management.
    
    Requirement: F-1, F-5, F-13, N-2, N-5
    Version: v01
    """
    
    def __init__(self, config: StoreConfig) -> None:
        """
        Initialize local store.
        
        Requirement: N-2, N-3
        Version: v01
        """
        self.config = config
        self.redactor = Redactor(config.redaction_enabled)
        self._init_directories()
        self._storage_records: list[StorageRecord] = []
    
    def save_screenshot(
        self,
        screenshot_path: Path,
        metadata: Optional[dict[str, Any]] = None,
        redact_regions: Optional[list[RedactionRegion]] = None
    ) -> StorageRecord:
        """
        Save screenshot with optional redaction.
        
        Requirement: F-5, F-13, F-14
        Version: v01
        """
        # Create dated folder
        date_folder = self._get_date_folder(self.config.screenshots_path)
        
        # Apply redaction if needed
        if self.config.redaction_enabled and (redact_regions or self.redactor.default_regions):
            screenshot_path = self.redactor.redact_screenshot(
                screenshot_path,
                redact_regions
            )
        
        # Generate filename if saving from temp location
        if screenshot_path.parent != date_folder:
            timestamp = datetime.now().strftime("%H%M%S-%f")
            seq_num = len(list(date_folder.glob("*.png"))) + 1
            filename = f"{timestamp}-{seq_num:04d}.png"
            dest_path = date_folder / filename
            
            shutil.copy2(screenshot_path, dest_path)
            screenshot_path = dest_path
        
        # Create storage record
        record = StorageRecord(
            id=str(uuid.uuid4()),
            storage_type=StorageType.SCREENSHOT,
            file_path=screenshot_path,
            timestamp=datetime.now(),
            size_bytes=screenshot_path.stat().st_size,
            metadata=metadata or {},
            is_redacted=bool(redact_regions or self.redactor.default_regions)
        )
        
        self._storage_records.append(record)
        
        _LOG.info(f"Saved screenshot: {screenshot_path}")
        return record
    
    def save_tasks(self, tasks_data: dict[str, Any]) -> StorageRecord:
        """
        Save task data to JSON file.
        
        Requirement: F-1, F-13
        Version: v01
        """
        tasks_path = self.config.base_path / self.config.tasks_file
        
        try:
            # Add timestamp
            tasks_data["last_saved"] = datetime.now().isoformat()
            
            # Save with pretty printing
            with open(tasks_path, 'w') as f:
                json.dump(tasks_data, f, indent=2)
            
            # Create record
            record = StorageRecord(
                id=str(uuid.uuid4()),
                storage_type=StorageType.TASK_DATA,
                file_path=tasks_path,
                timestamp=datetime.now(),
                size_bytes=tasks_path.stat().st_size,
                metadata={"task_count": len(tasks_data.get("tasks", []))}
            )
            
            self._storage_records.append(record)
            
            _LOG.info(f"Saved tasks to {tasks_path}")
            return record
            
        except Exception as e:
            _LOG.error(f"Failed to save tasks: {e}")
            raise
    
    def append_analysis_log(self, analysis_result: dict[str, Any]) -> None:
        """
        Append analysis result to JSONL log.
        
        Requirement: F-13
        Version: v01
        """
        log_folder = self._get_date_folder(self.config.logs_path)
        log_path = log_folder / self.config.analysis_file
        
        try:
            # Add timestamp if not present
            if "timestamp" not in analysis_result:
                analysis_result["timestamp"] = datetime.now().isoformat()
            
            # Append to JSONL file
            with open(log_path, 'a') as f:
                json.dump(analysis_result, f)
                f.write('\n')
            
            _LOG.debug(f"Appended analysis log entry")
            
        except Exception as e:
            _LOG.error(f"Failed to append analysis log: {e}")
    
    def append_escalation_log(self, escalation_event: dict[str, Any]) -> None:
        """
        Log escalation events.
        
        Requirement: F-13
        Version: v01
        """
        log_folder = self._get_date_folder(self.config.logs_path)
        log_path = log_folder / "escalation_log.jsonl"
        
        try:
            escalation_event["timestamp"] = datetime.now().isoformat()
            
            with open(log_path, 'a') as f:
                json.dump(escalation_event, f)
                f.write('\n')
            
            _LOG.info(f"Logged escalation event: {escalation_event.get('type')}")
            
        except Exception as e:
            _LOG.error(f"Failed to log escalation event: {e}")
    
    def load_tasks(self) -> Optional[dict[str, Any]]:
        """
        Load saved task data.
        
        Requirement: F-1
        Version: v01
        """
        tasks_path = self.config.base_path / self.config.tasks_file
        
        if not tasks_path.exists():
            return None
        
        try:
            with open(tasks_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            _LOG.error(f"Failed to load tasks: {e}")
            return None
    
    def get_recent_screenshots(
        self, 
        hours: int = 1,
        limit: Optional[int] = None
    ) -> list[Path]:
        """
        Get recent screenshot paths.
        
        Requirement: F-13
        Version: v01
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        screenshots = []
        
        # Check today's folder
        date_folder = self._get_date_folder(self.config.screenshots_path)
        
        for img_path in sorted(date_folder.glob("*.png"), reverse=True):
            if img_path.stat().st_mtime > cutoff_time.timestamp():
                screenshots.append(img_path)
                
                if limit and len(screenshots) >= limit:
                    break
        
        return screenshots
    
    def cleanup_old_data(self) -> int:
        """
        Remove data older than configured retention period.
        
        Requirement: F-13
        Version: v01
        """
        if self.config.max_storage_days <= 0:
            return 0
        
        cutoff_date = datetime.now() - timedelta(days=self.config.max_storage_days)
        removed_count = 0
        
        # Clean screenshots
        for date_folder in self.config.screenshots_path.iterdir():
            if not date_folder.is_dir():
                continue
            
            try:
                # Parse folder date
                folder_date = datetime.strptime(date_folder.name, "%Y%b%d")
                
                if folder_date < cutoff_date:
                    shutil.rmtree(date_folder)
                    removed_count += 1
                    _LOG.info(f"Removed old screenshot folder: {date_folder}")
                    
            except ValueError:
                # Skip folders that don't match date format
                continue
        
        # Clean logs
        for log_folder in self.config.logs_path.iterdir():
            if not log_folder.is_dir():
                continue
            
            try:
                folder_date = datetime.strptime(log_folder.name, "%Y%b%d")
                
                if folder_date < cutoff_date:
                    shutil.rmtree(log_folder)
                    removed_count += 1
                    _LOG.info(f"Removed old log folder: {log_folder}")
                    
            except ValueError:
                continue
        
        return removed_count
    
    def compress_old_screenshots(self, days_old: int = 7) -> int:
        """
        Compress screenshots older than specified days.
        
        Requirement: F-13
        Version: v01
        """
        if not self.config.compression_enabled:
            return 0
        
        cutoff_date = datetime.now() - timedelta(days=days_old)
        compressed_count = 0
        
        for date_folder in self.config.screenshots_path.iterdir():
            if not date_folder.is_dir():
                continue
            
            try:
                folder_date = datetime.strptime(date_folder.name, "%Y%b%d")
                
                if folder_date < cutoff_date:
                    # Compress each PNG file
                    for img_path in date_folder.glob("*.png"):
                        compressed_path = img_path.with_suffix(".png.gz")
                        
                        if not compressed_path.exists():
                            with open(img_path, 'rb') as f_in:
                                with gzip.open(compressed_path, 'wb') as f_out:
                                    shutil.copyfileobj(f_in, f_out)
                            
                            # Remove original after successful compression
                            img_path.unlink()
                            compressed_count += 1
                            
                            _LOG.debug(f"Compressed: {img_path}")
                            
            except ValueError:
                continue
        
        if compressed_count > 0:
            _LOG.info(f"Compressed {compressed_count} old screenshots")
        
        return compressed_count
    
    def _init_directories(self) -> None:
        """Create required directories."""
        self.config.base_path.mkdir(parents=True, exist_ok=True)
        self.config.screenshots_path.mkdir(parents=True, exist_ok=True)
        self.config.logs_path.mkdir(parents=True, exist_ok=True)
    
    def _get_date_folder(self, base_path: Path) -> Path:
        """Get or create today's date folder."""
        today = datetime.now().strftime("%Y%b%d").upper()
        date_folder = base_path / today
        date_folder.mkdir(exist_ok=True)
        return date_folder
```

## API Specification

### LocalStore Class

**Methods:**
- `save_screenshot(screenshot_path, metadata, redact_regions) -> StorageRecord` - Save screenshot with optional redaction
- `save_tasks(tasks_data) -> StorageRecord` - Persist task data
- `append_analysis_log(analysis_result) -> None` - Log analysis results
- `append_escalation_log(escalation_event) -> None` - Log escalation events
- `load_tasks() -> Optional[dict]` - Load saved tasks
- `get_recent_screenshots(hours, limit) -> list[Path]` - Get recent screenshots
- `cleanup_old_data() -> int` - Remove old data
- `compress_old_screenshots(days_old) -> int` - Compress old screenshots

### Redactor Class

**Methods:**
- `redact_screenshot(image_path, regions, output_path) -> Path` - Apply redaction
- `add_default_region(region) -> None` - Add always-redacted region
- `detect_sensitive_regions(image_path) -> list[RedactionRegion]` - Auto-detect sensitive content

## Example Usage

```python
from pathlib import Path
from src.local_store import LocalStore, StoreConfig, RedactionRegion

# Initialize
config = StoreConfig(
    base_path=Path("./data"),
    redaction_enabled=True,
    max_storage_days=30
)
store = LocalStore(config)

# Save screenshot with redaction
redact_regions = [
    RedactionRegion(x=100, y=200, width=300, height=50, reason="password_field")
]
record = store.save_screenshot(
    screenshot_path=Path("/tmp/screenshot.png"),
    metadata={"monitor": 1, "task": "coding"},
    redact_regions=redact_regions
)

# Save task data
tasks_data = {
    "tasks": [
        {"id": "1", "title": "Complete PRD", "priority": 1},
        {"id": "2", "title": "Code review", "priority": 2}
    ],
    "current_task_id": "1"
}
store.save_tasks(tasks_data)

# Log analysis result
store.append_analysis_log({
    "screenshot": str(record.file_path),
    "alignment": "misaligned",
    "confidence": 0.85,
    "task": "Complete PRD"
})

# Cleanup old data
removed = store.cleanup_old_data()
print(f"Removed {removed} old folders")
```

## Version History

- **v01** (2025-06-15): Initial implementation
  - Structured local storage with dated folders
  - Screenshot redaction for privacy
  - Automatic cleanup and compression
  - JSON/JSONL logging for all components
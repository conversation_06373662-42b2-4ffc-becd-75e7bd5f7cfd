# Reasoning AI Module PRD

## Overview
The Reasoning AI module analyzes screenshots to determine whether the user's current activity aligns with their highest-priority task. It serves as the core intelligence for productivity monitoring.

## Requirements Mapping

| PRD Requirement | Description | Implementation |
|----------------|-------------|----------------|
| **F-6** | Run reasoning agent every main_ai_interval_one seconds to evaluate alignment | `ReasoningEngine.analyze_alignment()` |
| **F-7** | When misalignment detected, prompt user for justification | `ReasoningEngine.detect_misalignment()` |
| **N-1** | Lightweight CPU usage < 5% average | Efficient image processing, caching |
| **N-2** | Modular with TypeHinted interfaces | All methods with type hints, passes mypy --strict |
| **N-3** | Configurable intervals via CFG | `ReasoningConfig` dataclass |
| **N-4** | Fail-safe operation | Error handling without crashing |

## Implementation Approach

```python
# ── /src/reasoning_ai/config.py ─────────────────────────────────────────────────
from __future__ import annotations

from dataclasses import dataclass
from typing import Final

__all__: Final = ["ReasoningConfig"]

@dataclass(slots=True)
class ReasoningConfig:
    """
    Configuration for reasoning AI.
    
    Requirement: N-3
    Version: v01
    """
    main_ai_interval_one: int = 5  # Normal mode interval (seconds)
    main_ai_interval_two: int = 1  # Escalation mode interval (seconds)
    confidence_threshold: float = 0.7  # Minimum confidence for alignment
    vision_model: str = "local"  # "local" or "cloud"
    cache_results: bool = True
    cache_ttl: int = 60  # Cache time-to-live in seconds
```

```python
# ── /src/reasoning_ai/models.py ─────────────────────────────────────────────────
from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Optional

__all__ = ["AnalysisResult", "AlignmentStatus", "ScreenContent"]

class AlignmentStatus(Enum):
    """Task alignment status."""
    ALIGNED = "aligned"
    MISALIGNED = "misaligned"
    UNCERTAIN = "uncertain"
    ERROR = "error"

@dataclass
class ScreenContent:
    """
    Extracted content from screenshot.
    
    Requirement: F-6
    Version: v01
    """
    screenshot_path: Path
    timestamp: datetime
    detected_text: list[str]
    detected_applications: list[str]
    detected_urls: list[str]
    raw_description: str

@dataclass
class AnalysisResult:
    """
    Result of task alignment analysis.
    
    Requirement: F-6, F-7
    Version: v01
    """
    screenshot_path: Path
    task_title: str
    alignment_status: AlignmentStatus
    confidence: float
    reasoning: str
    screen_content: ScreenContent
    timestamp: datetime
    processing_time_ms: float
```

```python
# ── /src/reasoning_ai/vision.py ─────────────────────────────────────────────────
from __future__ import annotations

import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Optional

from PIL import Image

from .models import ScreenContent

__all__ = ["VisionProcessor"]

_LOG = logging.getLogger(__name__)

class VisionProcessor:
    """
    Process screenshots to extract content.
    
    Requirement: F-6, N-1
    Version: v01
    """
    
    def __init__(self, model_type: str = "local") -> None:
        """
        Initialize vision processor.
        
        Requirement: N-1
        Version: v01
        """
        self.model_type = model_type
        self._init_model()
    
    def _init_model(self) -> None:
        """Initialize the vision model based on type."""
        if self.model_type == "local":
            # Placeholder for local model initialization
            # In production, this would load a lightweight local model
            _LOG.info("Initialized local vision model")
        else:
            # Placeholder for cloud model initialization
            _LOG.info("Initialized cloud vision model")
    
    def extract_content(self, screenshot_path: Path) -> Optional[ScreenContent]:
        """
        Extract content from screenshot.
        
        Requirement: F-6, N-1, N-4
        Version: v01
        """
        try:
            start_time = time.perf_counter()
            
            # Load image
            if not screenshot_path.exists():
                _LOG.error(f"Screenshot not found: {screenshot_path}")
                return None
            
            img = Image.open(screenshot_path)
            
            # Simulate content extraction
            # In production, this would use actual OCR/vision models
            content = ScreenContent(
                screenshot_path=screenshot_path,
                timestamp=datetime.now(),
                detected_text=self._extract_text(img),
                detected_applications=self._detect_applications(img),
                detected_urls=self._extract_urls(img),
                raw_description=self._generate_description(img)
            )
            
            elapsed_ms = (time.perf_counter() - start_time) * 1000
            _LOG.debug(f"Content extraction took {elapsed_ms:.1f}ms")
            
            return content
            
        except Exception as e:
            _LOG.error(f"Failed to extract content from {screenshot_path}: {e}")
            return None
    
    def _extract_text(self, img: Image.Image) -> list[str]:
        """Extract text from image using OCR."""
        # Placeholder implementation
        # In production: use pytesseract or similar
        return ["Sample text from screenshot"]
    
    def _detect_applications(self, img: Image.Image) -> list[str]:
        """Detect active applications from UI elements."""
        # Placeholder implementation
        # In production: analyze window titles, dock/taskbar
        return ["Chrome", "VS Code"]
    
    def _extract_urls(self, img: Image.Image) -> list[str]:
        """Extract URLs from browser address bars."""
        # Placeholder implementation
        # In production: OCR on browser UI regions
        return ["https://example.com"]
    
    def _generate_description(self, img: Image.Image) -> str:
        """Generate natural language description of screen content."""
        # Placeholder implementation
        # In production: use vision-language model
        return "User appears to be coding in VS Code with Chrome open"
```

```python
# ── /src/reasoning_ai/engine.py ─────────────────────────────────────────────────
from __future__ import annotations

import logging
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional

from .config import ReasoningConfig
from .models import AlignmentStatus, AnalysisResult
from .vision import VisionProcessor

__all__ = ["ReasoningEngine"]

_LOG = logging.getLogger(__name__)

class ReasoningEngine:
    """
    Main reasoning engine for task alignment detection.
    
    Requirement: F-6, F-7, N-1, N-2
    Version: v01
    """
    
    def __init__(self, config: ReasoningConfig) -> None:
        """
        Initialize reasoning engine.
        
        Requirement: N-2, N-3
        Version: v01
        """
        self.config = config
        self.vision = VisionProcessor(config.vision_model)
        self._cache: dict[Path, tuple[AnalysisResult, datetime]] = {}
    
    def analyze_alignment(
        self, 
        screenshot_path: Path, 
        current_task: str
    ) -> Optional[AnalysisResult]:
        """
        Analyze if screen content aligns with current task.
        
        Requirement: F-6, N-1, N-4
        Version: v01
        """
        try:
            start_time = time.perf_counter()
            
            # Check cache
            if self.config.cache_results:
                cached_result = self._get_cached_result(screenshot_path)
                if cached_result:
                    return cached_result
            
            # Extract screen content
            content = self.vision.extract_content(screenshot_path)
            if not content:
                return None
            
            # Perform alignment analysis
            alignment_status, confidence, reasoning = self._analyze_content_alignment(
                content, current_task
            )
            
            # Create result
            processing_time_ms = (time.perf_counter() - start_time) * 1000
            
            result = AnalysisResult(
                screenshot_path=screenshot_path,
                task_title=current_task,
                alignment_status=alignment_status,
                confidence=confidence,
                reasoning=reasoning,
                screen_content=content,
                timestamp=datetime.now(),
                processing_time_ms=processing_time_ms
            )
            
            # Cache result
            if self.config.cache_results:
                self._cache_result(screenshot_path, result)
            
            _LOG.info(
                f"Analysis complete: {alignment_status.value} "
                f"(confidence={confidence:.2f}, time={processing_time_ms:.1f}ms)"
            )
            
            return result
            
        except Exception as e:
            _LOG.error(f"Analysis failed for {screenshot_path}: {e}")
            return None
    
    def detect_misalignment(self, result: AnalysisResult) -> bool:
        """
        Determine if user should be prompted based on analysis.
        
        Requirement: F-7
        Version: v01
        """
        if result.alignment_status == AlignmentStatus.MISALIGNED:
            return result.confidence >= self.config.confidence_threshold
        
        return False
    
    def _analyze_content_alignment(
        self, 
        content: ScreenContent, 
        task: str
    ) -> tuple[AlignmentStatus, float, str]:
        """
        Core logic for determining task alignment.
        
        Requirement: F-6
        Version: v01
        """
        # Extract task keywords
        task_keywords = self._extract_keywords(task)
        
        # Check for keyword matches in screen content
        text_matches = sum(
            1 for keyword in task_keywords
            if any(keyword.lower() in text.lower() for text in content.detected_text)
        )
        
        # Check for relevant applications
        app_relevance = self._check_app_relevance(
            content.detected_applications, task
        )
        
        # Check URL relevance
        url_relevance = self._check_url_relevance(
            content.detected_urls, task
        )
        
        # Calculate overall alignment score
        total_keywords = len(task_keywords)
        if total_keywords == 0:
            alignment_score = 0.5
        else:
            alignment_score = (
                (text_matches / total_keywords) * 0.5 +
                app_relevance * 0.3 +
                url_relevance * 0.2
            )
        
        # Determine status
        if alignment_score >= 0.7:
            status = AlignmentStatus.ALIGNED
            reasoning = f"Screen content matches task '{task}'"
        elif alignment_score >= 0.3:
            status = AlignmentStatus.UNCERTAIN
            reasoning = f"Unclear if screen content relates to '{task}'"
        else:
            status = AlignmentStatus.MISALIGNED
            reasoning = f"Screen content does not match task '{task}'"
        
        return status, alignment_score, reasoning
    
    def _extract_keywords(self, task: str) -> list[str]:
        """Extract relevant keywords from task description."""
        # Simple keyword extraction
        # In production: use NLP for better extraction
        words = task.lower().split()
        stopwords = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to"}
        return [w for w in words if w not in stopwords and len(w) > 2]
    
    def _check_app_relevance(self, apps: list[str], task: str) -> float:
        """Check if detected applications are relevant to task."""
        task_lower = task.lower()
        
        # Define app relevance mappings
        app_keywords = {
            "code": ["vs code", "sublime", "atom", "intellij", "xcode"],
            "browser": ["chrome", "firefox", "safari", "edge"],
            "terminal": ["terminal", "iterm", "console"],
            "documentation": ["notion", "obsidian", "word", "pages"]
        }
        
        relevance_score = 0.0
        for category, app_list in app_keywords.items():
            if category in task_lower:
                for app in apps:
                    if any(keyword in app.lower() for keyword in app_list):
                        relevance_score = 1.0
                        break
        
        return relevance_score
    
    def _check_url_relevance(self, urls: list[str], task: str) -> float:
        """Check if detected URLs are relevant to task."""
        if not urls:
            return 0.5  # Neutral if no URLs detected
        
        task_lower = task.lower()
        relevant_domains = {
            "documentation": ["docs", "wiki", "manual"],
            "github": ["github.com", "gitlab.com"],
            "stackoverflow": ["stackoverflow.com"],
            "testing": ["localhost", "127.0.0.1", "staging"]
        }
        
        for category, domains in relevant_domains.items():
            if category in task_lower:
                for url in urls:
                    if any(domain in url.lower() for domain in domains):
                        return 1.0
        
        return 0.0
    
    def _get_cached_result(self, screenshot_path: Path) -> Optional[AnalysisResult]:
        """Get cached analysis result if still valid."""
        if screenshot_path in self._cache:
            result, cached_time = self._cache[screenshot_path]
            
            if datetime.now() - cached_time < timedelta(seconds=self.config.cache_ttl):
                _LOG.debug(f"Using cached result for {screenshot_path}")
                return result
            else:
                # Remove expired cache entry
                del self._cache[screenshot_path]
        
        return None
    
    def _cache_result(self, screenshot_path: Path, result: AnalysisResult) -> None:
        """Cache analysis result."""
        self._cache[screenshot_path] = (result, datetime.now())
        
        # Clean old entries if cache gets too large
        if len(self._cache) > 100:
            oldest_key = min(
                self._cache.keys(), 
                key=lambda k: self._cache[k][1]
            )
            del self._cache[oldest_key]
```

## API Specification

### ReasoningEngine Class

**Methods:**
- `analyze_alignment(screenshot_path: Path, current_task: str) -> Optional[AnalysisResult]` - Analyze task alignment
- `detect_misalignment(result: AnalysisResult) -> bool` - Check if user should be prompted

### AnalysisResult Model

**Fields:**
- `screenshot_path: Path` - Path to analyzed screenshot
- `task_title: str` - Current task being checked
- `alignment_status: AlignmentStatus` - Alignment determination
- `confidence: float` - Confidence score (0-1)
- `reasoning: str` - Human-readable explanation
- `screen_content: ScreenContent` - Extracted content details
- `timestamp: datetime` - Analysis timestamp
- `processing_time_ms: float` - Processing duration

## Example Usage

```python
from pathlib import Path
from src.reasoning_ai import ReasoningEngine, ReasoningConfig

# Initialize
config = ReasoningConfig(
    main_ai_interval_one=5,
    confidence_threshold=0.7,
    vision_model="local"
)
engine = ReasoningEngine(config)

# Analyze screenshot
screenshot = Path("./output/screenshots/2025JUN15/120000-0001.png")
current_task = "Complete PRD documentation"

result = engine.analyze_alignment(screenshot, current_task)

if result and engine.detect_misalignment(result):
    print(f"Misalignment detected: {result.reasoning}")
    print(f"Confidence: {result.confidence:.2%}")
    # Trigger user prompt for justification
```

## Version History

- **v01** (2025-06-15): Initial implementation
  - Basic vision processing framework
  - Keyword-based alignment detection
  - Application and URL relevance checking
  - Result caching for performance
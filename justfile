# Screenshot Taker Commands
# Run with: just <command>
#
# Note: If module imports fail, use the standalone script:


# Default: show available commands
default:
    @just --list

# ---- INTEGRATED IMPLEMENTATION (FULL SYSTEM) ----

# MEGA SINGLE FILE IMPLEMENTATION (ISOLATED)
mega:
    uv run python mega.py --tasks "Task1" "Task2" "Task3" --interval 5 --ai 5 --log-level INFO

# Start the full integrated Productivity Guard
start:
    uv run main.py start

# Show task listw
tasks:
    uv run main.py tasks

# Add a new task
add-task DESCRIPTION:
    uv run main.py add "{{DESCRIPTION}}"

# Load demo tasks
demo-tasks:
    uv run main.py demo

# Set focus to a specific task (by priority number)
focus TASK_NUM="1":
    uv run main.py focus {{TASK_NUM}}

# Mark a task as complete
complete TASK_NUM:
    uv run main.py complete {{TASK_NUM}}

# Show system status
status:
    uv run main.py status

# Show configuration
show-config:
    uv run main.py config

# ---- SCREENSHOT MODULE COMMANDS ----

# Run screenshot capture module
screenshot INTERVAL="5" SCREENS="1":
    uv run python -m src.screenshot_taker --interval {{INTERVAL}} --screens {{SCREENS}}

# Run screenshot capture with debug logging
screenshot-debug INTERVAL="5" SCREENS="1":
    uv run python -m src.screenshot_taker --interval {{INTERVAL}} --screens {{SCREENS}} --log-level DEBUG

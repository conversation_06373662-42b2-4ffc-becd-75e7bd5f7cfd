# ── /src/productivity_guard.py ────────────────────────────────────────────────
"""
End‑to‑end MVP for *Productivity Guard* (Milestone 1–3).

Each public function/class is annotated with:
    • Requirement(s) it fulfils, e.g. "F‑4".
    • Version tag (v01). Bump when editing behaviour or signature.

The module starts two background threads:
    1. Screenshot loop  (F‑4, F‑5)
    2. AI/escrow loop   (F‑6 … F‑12)

All data stay local (F‑13), and a redaction stub is present for F‑14.
"""
from __future__ import annotations

import itertools
import json
import logging
import queue
import signal
import threading
import time
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Final, Iterable, List, Sequence

import mss
import pyautogui
from PIL import Image, ImageFilter

# --------------------------------------------------------------------------- CFG
@dataclass(slots=True)
class CFG:  # noqa: D101  – Config holder
    """
    Runtime‑editable configuration.

    Requirement: N‑3
    Version: v01
    """
    # Screenshot / AI cadence
    screenshot_interval: int = 5          # F‑4
    main_ai_interval_one: int = 5         # F‑6
    main_ai_interval_two: int = 1         # F‑10 (Escalation)
    justification_timeout: int = 60       # F‑9
    notification_every: int = 10          # F‑11
    # Monitors
    screens_to_capture: List[int] = field(default_factory=lambda: [1])  # F‑4
    # Storage
    output_root: Path = Path("./output")  # F‑5 / F‑13
    # Redaction
    blur_radius: int = 0                  # F‑14 (0 = no redaction)
    LOGLEVEL = "DEBUG"
    

# ---------------------------------------------------------------------- Helpers
_LOG = logging.getLogger("ProductivityGuard")
_LOG.setLevel(logging.INFO)


def _todays_folder(root: Path, kind: str) -> Path:
    """
    Build `{root}/{kind}/YYYYMMMDD/`, creating if absent.

    Requirement: F‑5
    Version: v01
    """
    today = datetime.now().strftime("%Y%b%d").upper()
    dest = root / kind / today
    dest.mkdir(parents=True, exist_ok=True)
    return dest


def _timestamp() -> str:
    """Return `HHMMSS‑µs` stamp.  Requirement: F‑5 | v01"""
    return datetime.now().strftime("%H%M%S-%f")


# ---------------------------------------------------------------- Task Manager
class TaskManager:
    """
    CRUD for the day's task list.

    Requirement: F‑1 … F‑3
    Version: v01
    """

    def __init__(self) -> None:
        self._tasks: list[str] = []
        self._pointer: int = 0
        self._lock = threading.Lock()

    # --------------------- public ------------------------------------------------
    def import_tasks(self, tasks: list[str]) -> None:
        """F‑1 | v01 – Load new task list (overwrites any existing)."""
        with self._lock:
            self._tasks = tasks[:]
            self._pointer = 0

    def mark_complete(self) -> None:
        """F‑3 | v01 – Mark current task done and advance pointer."""
        with self._lock:
            if self._pointer < len(self._tasks):
                self._pointer += 1

    def reorder(self, new_order: list[str]) -> None:
        """F‑3 | v01 – Replace ordering with user‑specified list."""
        with self._lock:
            self._tasks = new_order
            self._pointer = 0

    def current_task(self) -> str | None:
        """Return Priority #1 (or next) – F‑2 | v01."""
        with self._lock:
            return self._tasks[self._pointer] if self._pointer < len(self._tasks) else None


# ------------------------------------------------------------- Screenshot Taker
class ScreenshotTaker:
    """
    Capture and persist screenshots on demand.

    Requirement: F‑4, F‑5, N‑4
    Version: v01
    """

    def __init__(self, cfg: CFG) -> None:
        self.cfg = cfg
        self._seq_gen = itertools.count(1)

    # --------------------- public ------------------------------------------------
    def capture_once(self) -> list[Path]:
        """Return list of image paths captured.  Requirement: F‑4 | v01."""
        paths: list[Path] = []
        with mss.mss() as sct:
            total = len(sct.monitors) - 1
            targets = [i for i in self.cfg.screens_to_capture if 1 <= i <= total]
            if not targets:
                _LOG.warning("No valid monitors to capture")
                return paths

            folder = _todays_folder(self.cfg.output_root, "screenshots")

            for mon_idx in targets:
                try:
                    raw = sct.grab(sct.monitors[mon_idx])
                    img = Image.frombytes("RGB", raw.size, raw.rgb)

                    # Optional redaction (blur)
                    if self.cfg.blur_radius > 0:
                        img = img.filter(ImageFilter.GaussianBlur(self.cfg.blur_radius))

                    filename = f"{_timestamp()}-{next(self._seq_gen):04d}.png"
                    path = folder / filename
                    img.save(path, "PNG")
                    paths.append(path)
                except Exception as exc:  # noqa: BLE001
                    _LOG.warning("Failed capture %s: %s", mon_idx, exc, exc_info=True)
        return paths


# ---------------------------------------------------------------- Reasoning AI
class ReasoningAgent:
    """
    Decide if a screenshot matches the active task.

    Requirement: F‑6 … F‑8
    Version: v01
    """

    def __init__(self, cfg: CFG) -> None:
        self.cfg = cfg

    # --------------------- public ------------------------------------------------
    def on_task(self, image_path: Path, task: str | None) -> bool:
        """
        Dummy keyword heuristic: `task` word appears in filename ⇒ on‑task.

        Replace with vision model later. Requirement: F‑6 | v01
        """
        if task is None:
            return True  # nothing to enforce
        return task.lower().split()[0] in image_path.name.lower()


# ----------------------------------------------------------------- Escalator
class Escalator:
    """
    Handle prompting, notifications, and punitive actions.

    Requirement: F‑7 … F‑12
    Version: v01
    """

    def __init__(self, cfg: CFG) -> None:
        self.cfg = cfg
        self._strike_count: int = 0
        self._last_notify: float = 0.0
        self._in_escalation: bool = False

    # --------------------- public ------------------------------------------------
    def reset(self) -> None:
        """Back to normal mode.  Requirement: F‑10 | v01."""
        self._strike_count = 0
        self._in_escalation = False

    def process_misalignment(self, justification: str | None) -> None:
        """
        Decide next step after no / bad justification.

        Requirement: F‑9 … F‑12
        Version: v01
        """
        if justification:  # treat any reply as valid
            self.reset()
            return

        self._strike_count += 1
        if self._strike_count == 1:
            self._in_escalation = True

        now = time.time()
        if now - self._last_notify >= self.cfg.notification_every:
            self._notify_user()
            self._last_notify = now

        if self._strike_count >= 3:
            self._close_tab()

    # ------------------- internal ----------------------------------------------
    def _notify_user(self) -> None:
        """Console notification; replace w/ desktop later.  F‑11 | v01."""
        print("⚠️  Productivity Guard: please return to Priority #1 or justify the switch.")

    def _close_tab(self) -> None:
        """Send Cmd+W once.  Requirement: F‑12 | v01."""
        try:
            pyautogui.hotkey("command", "w")
            _LOG.info("Escalator sent Cmd+W")
        except Exception as exc:  # noqa: BLE001
            _LOG.warning("Cmd+W failed: %s", exc, exc_info=True)

    # ---------------- properties -----------------------------------------------
    @property
    def ai_interval(self) -> int:
        """Current poll interval (F‑6 / F‑10)."""
        return self.cfg.main_ai_interval_two if self._in_escalation else self.cfg.main_ai_interval_one


# ----------------------------------------------------------------- Main Runner
class ProductivityGuardRunner:
    """
    Orchestrate Screenshot, AI, and Escalation loops.

    Requirement: N‑1, N‑2
    Version: v01
    """

    def __init__(self, cfg: CFG, tasks: list[str]) -> None:
        self.cfg = cfg
        self.tasks = TaskManager()
        self.tasks.import_tasks(tasks)

        self.taker = ScreenshotTaker(cfg)
        self.ai = ReasoningAgent(cfg)
        self.escalator = Escalator(cfg)

        self._stop = threading.Event()
        self._image_queue: "queue.Queue[Path]" = queue.Queue()

        self._threads: list[threading.Thread] = []

    # ------------------------- lifecycle ---------------------------------------
    def start(self) -> None:
        """Spawn loops (non‑blocking).  Requirement: N‑1 | v01."""
        self._threads = [
            threading.Thread(target=self._screenshot_loop, daemon=True),
            threading.Thread(target=self._ai_loop, daemon=True),
        ]
        for t in self._threads:
            t.start()
        _LOG.info("Productivity Guard started.")

    def stop(self) -> None:
        """Signal threads to exit.  Requirement: N‑1 | v01."""
        self._stop.set()
        for t in self._threads:
            t.join(timeout=2)
        _LOG.info("Productivity Guard stopped.")

    # --------------------------- loops -----------------------------------------
    def _screenshot_loop(self) -> None:
        """Periodically enqueue screenshot paths.  F‑4 | v01."""
        while not self._stop.is_set():
            paths = self.taker.capture_once()
            for p in paths:
                self._image_queue.put(p)
            time.sleep(self.cfg.screenshot_interval)

    def _ai_loop(self) -> None:
        """Poll queue and enforce alignment.  F‑6 … F‑12 | v01."""
        last_check = 0.0
        while not self._stop.is_set():
            try:
                img_path = self._image_queue.get(timeout=0.5)
            except queue.Empty:
                img_path = None

            interval = self.escalator.ai_interval
            if time.time() - last_check >= interval and img_path:
                last_check = time.time()
                on_task = self.ai.on_task(img_path, self.tasks.current_task())
                if not on_task:
                    justification = self._prompt_user()
                    self.escalator.process_misalignment(justification)
                else:
                    self.escalator.reset()

    # ------------------------- helpers -----------------------------------------
    def _prompt_user(self) -> str | None:
        """Blocking prompt ≤ justification_timeout.  F‑7, F‑8 | v01."""
        print("\n🚨 It looks like you've switched tasks. Why?")
        reply = self._timed_input(self.cfg.justification_timeout)
        self._log_switch(reply)
        return reply

    @staticmethod
    def _timed_input(timeout: int) -> str | None:
        """Return user input or None after `timeout` seconds.  F‑9 | v02. """
        print(f"(You have {timeout}s to respond, press Enter to skip) > ", end="", flush=True)
        # Simple timeout approach - in production would use proper async input
        import sys
        import os
        
        # Set stdin to non-blocking mode
        import fcntl
        fd = sys.stdin.fileno()
        old_flags = fcntl.fcntl(fd, fcntl.F_GETFL)
        fcntl.fcntl(fd, fcntl.F_SETFL, old_flags | os.O_NONBLOCK)
        
        try:
            end = time.time() + timeout
            result = ""
            while time.time() < end:
                try:
                    char = sys.stdin.read(1)
                    if char == '\n':
                        return result.strip() if result else None
                    elif char:
                        result += char
                except IOError:
                    pass
                time.sleep(0.1)
            print("\n⏰ Timeout.")
            return None
        finally:
            # Restore blocking mode
            fcntl.fcntl(fd, fcntl.F_SETFL, old_flags)

    def _log_switch(self, justification: str | None) -> None:
        """Append to JSONL audit file.  F‑3 | v01."""
        folder = _todays_folder(self.cfg.output_root, "logs")
        line = {
            "ts": _timestamp(),
            "task": self.tasks.current_task(),
            "justification": justification,
        }
        (folder / "switch_log.jsonl").open("a").write(json.dumps(line) + "\n")


# ----------------------------------------------------------------------  CLI
def _parse_cli() -> tuple[CFG, list[str]]:
    import argparse

    p = argparse.ArgumentParser(description="Run Productivity Guard MVP")
    p.add_argument("--tasks", nargs="+", required=True, help="Priority‑ordered task list")
    p.add_argument("--interval", type=int, default=5, help="screenshot interval (s)")
    p.add_argument("--ai", type=int, default=5, help="AI poll interval (s)")
    p.add_argument("--alert", type=int, default=1, help="AI interval in escalation (s)")
    p.add_argument("--screens", type=int, nargs="+", default=[1], help="monitor indices")
    p.add_argument("--blur", type=int, default=0, help="blur radius (px)")
    p.add_argument("--log-level", default=None, help="log level (default: CFG.LOGLEVEL)")
    args = p.parse_args()

    # Use CFG.LOGLEVEL if not specified
    log_level = args.log_level.upper() if args.log_level else CFG.LOGLEVEL
    logging.basicConfig(level=log_level,
                        format="%(asctime)s [%(levelname)s] %(message)s")

    cfg = CFG(
        screenshot_interval=max(1, args.interval),
        main_ai_interval_one=max(1, args.ai),
        main_ai_interval_two=max(1, args.alert),
        screens_to_capture=args.screens,
        blur_radius=max(0, args.blur),
    )
    return cfg, args.tasks


def _main() -> None:
    cfg, tasks = _parse_cli()
    guard = ProductivityGuardRunner(cfg, tasks)

    print("Starting Productivity Guard...")

    # graceful Ctrl‑C
    signal.signal(signal.SIGINT, lambda *_: guard.stop() or exit(0))
    guard.start()
    while True:
        time.sleep(1)


if __name__ == "__main__":
    _main()

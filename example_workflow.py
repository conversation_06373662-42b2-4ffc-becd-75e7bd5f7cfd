#!/usr/bin/env python3
"""
Example workflow demonstrating the integrated Productivity Guard system.

This script shows how to:
1. Load tasks
2. Start screenshot capture
3. Monitor tasks (with mock AI)
4. Handle user interaction
"""

import asyncio
import logging
from pathlib import Path
from datetime import datetime

from src.interfaces import (
    SystemConfig,
    TaskPriority,
    TaskStatus,
    AIAnalysisResult,
    JustificationRequest,
    JustificationResponse,
    EscalationLevel
)
from src.main_app import ProductivityGuardApp


class ExampleWorkflow:
    """Example workflow demonstrating the system."""
    
    def __init__(self):
        """Initialize the example workflow."""
        self.logger = logging.getLogger(__name__)
        
        # Create example configuration
        self.config = SystemConfig(
            screenshot_interval=5,  # Take screenshots every 5 seconds
            main_ai_interval_one=10,  # Normal AI check every 10 seconds
            main_ai_interval_two=3,   # Escalated AI check every 3 seconds
            screens_to_capture=[1],   # Only capture main screen for demo
            justification_timeout=30,  # 30 second timeout for responses
            output_dir=Path("./output/example_run")
        )
        
        self.app = ProductivityGuardApp(self.config)
        
    async def setup_example_tasks(self):
        """Set up example tasks for demonstration."""
        self.logger.info("Setting up example tasks...")
        
        # Add some example tasks
        tasks = [
            ("Complete quarterly report", TaskPriority.P1),
            ("Review pull requests", TaskPriority.P2),
            ("Update documentation", TaskPriority.P3),
            ("Team meeting preparation", TaskPriority.P2),
            ("Fix bug in authentication", TaskPriority.P1),
        ]
        
        for description, priority in tasks:
            task = self.app.task_manager.add_task(description, priority)
            self.logger.info(f"Added task: {task.description} (Priority: {priority.value})")
            
        # Mark one task as in progress
        all_tasks = self.app.task_manager.get_all_tasks()
        if all_tasks:
            self.app.task_manager.update_task_status(
                all_tasks[0].id, 
                TaskStatus.IN_PROGRESS
            )
            self.logger.info(f"Set task '{all_tasks[0].description}' to IN_PROGRESS")
            
    async def mock_ai_behavior(self):
        """Simulate AI analysis behavior for demonstration."""
        await asyncio.sleep(5)  # Let system run normally first
        
        # Inject a mock off-task detection
        self.logger.info("=== DEMO: Simulating off-task detection ===")
        
        # Get current task
        current_task = self.app.task_manager.get_current_task()
        if current_task:
            # Create mock AI result
            mock_result = AIAnalysisResult(
                on_task=False,
                confidence=0.85,
                reason="Browsing social media (Twitter) instead of working",
                screenshot_ref="/mock/screenshot.png"
            )
            
            # Manually trigger the analysis processing
            await self.app._process_analysis_result(mock_result, current_task)
            
            self.logger.info("=== DEMO: Off-task behavior processed ===")
            
    async def run_demo(self, duration_seconds: int = 60):
        """Run the demo for specified duration."""
        self.logger.info(f"Starting demo workflow for {duration_seconds} seconds...")
        
        # Set up example tasks
        await self.setup_example_tasks()
        
        # Start the app in background
        app_task = asyncio.create_task(self.app.start())
        
        # Run mock AI behavior in background
        mock_task = asyncio.create_task(self.mock_ai_behavior())
        
        try:
            # Run for specified duration
            await asyncio.sleep(duration_seconds)
            
            self.logger.info("Demo duration complete, shutting down...")
            
        finally:
            # Shutdown the app
            await self.app.shutdown()
            
            # Cancel tasks
            app_task.cancel()
            mock_task.cancel()
            
            # Wait for cleanup
            await asyncio.gather(app_task, mock_task, return_exceptions=True)
            
        # Print summary
        self._print_summary()
        
    def _print_summary(self):
        """Print summary of the demo run."""
        print("\n" + "="*60)
        print("DEMO SUMMARY")
        print("="*60)
        
        # Show task status
        print("\nTask Status:")
        tasks = self.app.task_manager.get_all_tasks()
        for task in tasks:
            print(f"  - [{task.status.value}] {task.description} (P{task.priority.value})")
            
        # Show escalation state
        escalation_state = self.app.escalation_manager.get_current_state()
        print(f"\nEscalation Level: {escalation_state.level.name}")
        print(f"Prompts at this level: {escalation_state.prompt_count}")
        
        # Show output location
        print(f"\nOutput saved to: {self.config.output_dir}")
        print("  - Screenshots: output/example_run/screenshots/")
        print("  - Logs: output/example_run/logs/")
        print("  - Task data: output/example_run/tasks.json")
        
        print("\n" + "="*60)


async def main():
    """Main entry point for example workflow."""
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create and run workflow
    workflow = ExampleWorkflow()
    
    print("\n" + "="*60)
    print("PRODUCTIVITY GUARD - EXAMPLE WORKFLOW")
    print("="*60)
    print("\nThis demo will:")
    print("1. Create example tasks")
    print("2. Start screenshot capture")
    print("3. Simulate AI monitoring")
    print("4. Demonstrate off-task detection")
    print("5. Show user interaction flow")
    print("\nPress Ctrl+C to stop early.\n")
    
    try:
        # Run demo for 60 seconds
        await workflow.run_demo(duration_seconds=60)
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user.")
        await workflow.app.shutdown()


if __name__ == "__main__":
    # Run the example
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nExample terminated.")
    except Exception as e:
        logging.error(f"Example failed: {e}", exc_info=True)
        raise
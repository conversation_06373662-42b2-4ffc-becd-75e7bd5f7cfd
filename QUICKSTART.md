# Productivity Guard - Quick Start Guide

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Feisty4
   ```

2. **Install dependencies**
   ```bash
   pip install -e .
   # or with uv:
   uv pip install -e .
   ```

3. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env and add your OpenAI API key
   ```

## Basic Usage

### Start monitoring with demo tasks
```bash
python main.py start --demo
```

### View available commands
```bash
python main.py --help
```

### Common Commands

**Task Management:**
```bash
# List all tasks
python main.py tasks

# Add a new task
python main.py tasks --add "Complete code review" --priority P1

# Complete a task
python main.py tasks --complete <task-id>
```

**System Control:**
```bash
# Start monitoring
python main.py start

# Check status
python main.py status

# Generate report
python main.py report
```

**Configuration:**
```bash
# Create default config file
python main.py config --create

# Show current config
python main.py config --show

# Edit config value
python main.py config --edit screenshot_interval=60
```

## Running Tests

### Comprehensive system test
```bash
python test_full_system.py
```

### Run demo scenario
```bash
python main.py demo
```

## Output Structure

All data is saved to the `./output` directory:
- `screenshots/` - Captured screenshots organized by date
- `logs/` - System and analysis logs
- `tasks/` - Task data persistence
- `system_test/` - Test results and reports

## Key Features

1. **Automatic Screenshot Capture** - Every 2 minutes
2. **AI Task Analysis** - Every 5 minutes (normal) or 2 minutes (escalated)
3. **Off-Task Detection** - Alerts when not working on assigned task
4. **Justification System** - Explain breaks or context switches
5. **Escalation Levels** - Adaptive monitoring based on behavior
6. **Local Data Storage** - All data stays on your machine

## Tips

- Use `--demo` flag to start with sample tasks
- Check `output_examples/` for sample data formats
- Run `python main.py test` for system verification
- Logs are stored with timestamps for debugging
# Escalator Module

The Escalator module manages the progressive escalation of interventions when users exhibit persistent off-task behavior. It implements a multi-level system that increases monitoring frequency and intervention severity based on user responsiveness.

## Escalation Levels

1. **NORMAL** - Default monitoring mode (5-minute intervals)
2. **WARNING** - First warning issued (3-minute intervals)
3. **FREQUENT** - Increased monitoring (1-minute intervals)
4. **AGGRESSIVE** - Active interventions (30-second intervals, tab closing)

## Key Features

### Justification Validation
- Validates user justifications for off-task behavior
- Checks minimum length and task relevance
- Prevents escalation when valid justifications are provided

### Progressive Interventions
- **Notification** - Simple alerts to the user
- **Frequent Checking** - Reduced monitoring intervals
- **Tab Closing** - Closes distracting applications
- **Screen Lock** - Extreme intervention (not automatically triggered)

### De-escalation
- Automatically de-escalates after sustained on-task behavior
- Configurable time thresholds (default: 5 minutes)
- Resets prompt counts when moving between levels

### Tracking
- Records all justification prompts and responses
- Tracks unanswered prompts within time windows
- Maintains history of off-task incidents

## Configuration

The `EscalatorConfig` class provides extensive customization:

```python
config = EscalatorConfig(
    prompts_before_escalation=3,  # Prompts before level increase
    de_escalation_time_minutes=5,  # Minutes of good behavior to de-escalate
    justification_timeout_seconds=60,  # Time to wait for response
    max_ignored_prompts=10,  # Maximum ignored prompts before forcing intervention
    min_justification_length=10,  # Minimum chars for valid justification
    require_task_relevance=True  # Justification must mention the task
)
```

## Usage Example

```python
from src.escalator import Escalator, EscalatorConfig

# Create escalator instance
escalator = Escalator(EscalatorConfig())

# Process an off-task detection
analysis = AIAnalysisResult(
    on_task=False,
    confidence=0.9,
    reason="Browsing social media",
    screenshot_ref="/path/to/screenshot.png"
)
state = escalator.process_analysis(analysis)

# Check if intervention needed
if escalator.should_intervene():
    action = escalator.get_intervention_action()
    escalator.execute_intervention()

# Get current monitoring interval
interval = escalator.get_monitoring_interval()
```

## Integration Points

The Escalator module integrates with:
- **AI Reasoning** - Receives analysis results
- **User Interface** - Sends notification requests
- **Main Orchestrator** - Provides monitoring intervals
- **Local Storage** - Logs escalation history

## Platform Support

- **macOS** - Full support including app closing
- **Windows** - Basic support (tab closing not implemented)
- **Linux** - Basic support (tab closing not implemented)
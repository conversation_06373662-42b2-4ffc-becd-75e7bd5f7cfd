"""Configuration for Escalator module."""

from dataclasses import dataclass, field
from typing import Dict

@dataclass
class EscalatorConfig:
    """Configuration settings for the escalation system.
    
    Manages thresholds, intervals, and intervention settings for 
    escalating off-task behavior interventions.
    """
    
    # Escalation thresholds
    prompts_before_escalation: int = 3  # Number of unanswered prompts before escalating
    de_escalation_time_minutes: int = 5  # Minutes of on-task behavior before de-escalating
    
    # Notification intervals (seconds)
    normal_check_interval: int = 300  # 5 minutes in normal mode
    warning_check_interval: int = 180  # 3 minutes after first warning
    frequent_check_interval: int = 60  # 1 minute in frequent mode
    aggressive_check_interval: int = 30  # 30 seconds in aggressive mode
    
    # Intervention settings
    justification_timeout_seconds: int = 60  # Time to wait for user justification
    max_ignored_prompts: int = 10  # Maximum prompts before forcing intervention
    aggressive_apps_to_close: list[str] = field(default_factory=lambda: [
        "Twitter", "Facebook", "Reddit", "YouTube", "Instagram", 
        "TikTok", "Discord", "Slack", "WhatsApp"
    ])
    
    # Justification validation
    min_justification_length: int = 10  # Minimum characters for valid justification
    require_task_relevance: bool = True  # Whether justification must mention the task
    
    # Escalation level intervals - how long to stay at each level
    level_intervals: Dict[str, int] = field(default_factory=lambda: {
        "WARNING": 900,  # 15 minutes
        "FREQUENT": 1800,  # 30 minutes  
        "AGGRESSIVE": 3600  # 60 minutes
    })
    
    def get_check_interval(self, level_name: str) -> int:
        """Get the check interval for a given escalation level."""
        intervals = {
            "NORMAL": self.normal_check_interval,
            "WARNING": self.warning_check_interval,
            "FREQUENT": self.frequent_check_interval,
            "AGGRESSIVE": self.aggressive_check_interval
        }
        return intervals.get(level_name, self.normal_check_interval)
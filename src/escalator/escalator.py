"""Main implementation of the Escalator system."""

import asyncio
import logging
import subprocess
import platform
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

from ..interfaces import (
    EscalationManagerInterface,
    EscalationState,
    EscalationLevel,
    AIAnalysisResult,
    JustificationResponse,
    JustificationRequest
)
from .config import EscalatorConfig


class InterventionAction(Enum):
    """Types of intervention actions the system can take."""
    NONE = "none"
    NOTIFY = "notify"
    FREQUENT_CHECK = "frequent_check" 
    CLOSE_TABS = "close_tabs"
    LOCK_SCREEN = "lock_screen"


@dataclass
class PromptRecord:
    """Record of a justification prompt sent to the user."""
    timestamp: datetime
    request: JustificationRequest
    response: Optional[JustificationResponse] = None
    was_answered: bool = False


class Escalator(EscalationManagerInterface):
    """
    Manages escalation state and interventions for off-task behavior.
    
    Implements a multi-level escalation system that progressively increases
    interventions based on repeated off-task behavior and ignored prompts.
    """
    
    def __init__(self, config: Optional[EscalatorConfig] = None):
        """Initialize the escalator with configuration."""
        self.config = config or EscalatorConfig()
        self.logger = logging.getLogger(__name__)
        
        # Initialize state
        self._current_state = EscalationState(
            level=EscalationLevel.NORMAL,
            started_at=datetime.now(),
            prompt_count=0
        )
        
        # Track prompts and responses
        self._prompt_history: List[PromptRecord] = []
        self._consecutive_on_task_start: Optional[datetime] = None
        
        # Track off-task incidents for analytics
        self._off_task_history: List[Tuple[datetime, AIAnalysisResult]] = []
        
    def get_current_state(self) -> EscalationState:
        """Get current escalation state."""
        return self._current_state
        
    def check_justification(
        self, 
        justification: str,
        current_task: Optional[str] = None
    ) -> bool:
        """
        Validate if a user's justification is acceptable.
        
        Args:
            justification: The user's justification text
            current_task: Description of what they should be doing
            
        Returns:
            True if justification is valid, False otherwise
        """
        # Check minimum length
        if len(justification.strip()) < self.config.min_justification_length:
            self.logger.info(f"Justification too short: {len(justification)} chars")
            return False
            
        # Check if justification mentions the task (if required)
        if self.config.require_task_relevance and current_task:
            # Simple keyword matching - could be made more sophisticated
            task_words = set(current_task.lower().split())
            justification_words = set(justification.lower().split())
            
            # Check for at least some overlap
            if not task_words & justification_words:
                self.logger.info("Justification doesn't mention current task")
                return False
                
        # Additional validation could be added here (e.g., AI-based relevance check)
        return True
        
    def process_analysis(
        self, 
        analysis: AIAnalysisResult,
        justification: Optional[JustificationResponse] = None
    ) -> EscalationState:
        """
        Process analysis result and update escalation state.
        
        Args:
            analysis: AI analysis result
            justification: User's justification if provided
            
        Returns:
            Updated escalation state
        """
        if analysis.on_task:
            # User is on task - track for potential de-escalation
            if self._consecutive_on_task_start is None:
                self._consecutive_on_task_start = datetime.now()
                
            # Check if we should de-escalate
            if self._current_state.level != EscalationLevel.NORMAL:
                time_on_task = datetime.now() - self._consecutive_on_task_start
                if time_on_task >= timedelta(minutes=self.config.de_escalation_time_minutes):
                    self.de_escalate()
                    
            return self._current_state
            
        # User is off-task
        self._consecutive_on_task_start = None  # Reset on-task timer
        self._off_task_history.append((datetime.now(), analysis))
        
        # Check if justification was provided and valid
        if justification:
            # Record the prompt response
            if self._prompt_history and not self._prompt_history[-1].was_answered:
                self._prompt_history[-1].response = justification
                self._prompt_history[-1].was_answered = True
                
            if justification.valid:
                self.logger.info("Valid justification provided, not escalating")
                return self._current_state
                
        # Increment prompt count
        self._current_state.prompt_count += 1
        
        # Check if we should escalate
        if self._current_state.prompt_count >= self.config.prompts_before_escalation:
            self.escalate()
            
        return self._current_state
        
    def escalate(self) -> EscalationState:
        """Move to next escalation level."""
        current_level_value = self._current_state.level.value
        
        # Find next level
        next_level = None
        for level in EscalationLevel:
            if level.value == current_level_value + 1:
                next_level = level
                break
                
        if next_level:
            self.logger.warning(
                f"Escalating from {self._current_state.level.name} to {next_level.name}"
            )
            self._current_state = EscalationState(
                level=next_level,
                started_at=datetime.now(),
                prompt_count=0
            )
        else:
            # Already at max level
            self.logger.warning("Already at maximum escalation level")
            
        return self._current_state
        
    def de_escalate(self) -> EscalationState:
        """Return to previous escalation level."""
        current_level_value = self._current_state.level.value
        
        # Find previous level  
        prev_level = None
        for level in EscalationLevel:
            if level.value == current_level_value - 1:
                prev_level = level
                break
                
        if prev_level:
            self.logger.info(
                f"De-escalating from {self._current_state.level.name} to {prev_level.name}"
            )
            self._current_state = EscalationState(
                level=prev_level,
                started_at=datetime.now(),
                prompt_count=0
            )
            
        return self._current_state
        
    def should_intervene(self) -> bool:
        """
        Check if intervention is needed based on current state.
        
        Returns:
            True if intervention should be executed
        """
        # Check if we've exceeded max ignored prompts
        unanswered_count = self.get_unanswered_prompt_count()
        if unanswered_count >= self.config.max_ignored_prompts:
            return True
            
        # Check based on escalation level
        if self._current_state.level == EscalationLevel.AGGRESSIVE:
            # In aggressive mode, intervene after any unanswered prompt
            return unanswered_count > 0
            
        return False
        
    def get_intervention_action(self) -> InterventionAction:
        """
        Determine appropriate intervention action based on state.
        
        Returns:
            The intervention action to take
        """
        level = self._current_state.level
        
        if level == EscalationLevel.NORMAL:
            return InterventionAction.NONE
            
        elif level == EscalationLevel.WARNING:
            return InterventionAction.NOTIFY
            
        elif level == EscalationLevel.FREQUENT:
            return InterventionAction.FREQUENT_CHECK
            
        elif level == EscalationLevel.AGGRESSIVE:
            # Check how many prompts have been ignored
            unanswered = self.get_unanswered_prompt_count()
            if unanswered >= 3:
                return InterventionAction.CLOSE_TABS
            else:
                return InterventionAction.NOTIFY
                
        return InterventionAction.NONE
        
    def execute_intervention(self) -> None:
        """Execute intervention based on current escalation level."""
        action = self.get_intervention_action()
        
        if action == InterventionAction.NONE:
            pass
            
        elif action == InterventionAction.NOTIFY:
            self.logger.info("Executing notification intervention")
            # Notification handled by UI module
            
        elif action == InterventionAction.FREQUENT_CHECK:
            self.logger.info("Executing frequent monitoring intervention")
            # Interval change handled by main loop
            
        elif action == InterventionAction.CLOSE_TABS:
            self.logger.warning("Executing tab closing intervention!")
            self._close_distracting_tabs()
            
        elif action == InterventionAction.LOCK_SCREEN:
            self.logger.error("Executing screen lock intervention!")
            self._lock_screen()
            
    def reset_escalation(self) -> None:
        """Reset escalation to normal mode."""
        self.logger.info(
            f"Resetting escalation from {self._current_state.level.name} to NORMAL"
        )
        self._current_state = EscalationState(
            level=EscalationLevel.NORMAL,
            started_at=datetime.now(),
            prompt_count=0
        )
        self._consecutive_on_task_start = None
        
        # Keep only recent history
        cutoff = datetime.now() - timedelta(hours=1)
        self._off_task_history = [
            (ts, result) for ts, result in self._off_task_history
            if ts > cutoff
        ]
        self._prompt_history = [
            prompt for prompt in self._prompt_history
            if prompt.timestamp > cutoff
        ]
        
    def record_prompt(self, request: JustificationRequest) -> None:
        """Record that a justification prompt was sent."""
        self._prompt_history.append(PromptRecord(
            timestamp=datetime.now(),
            request=request,
            was_answered=False
        ))
        
    def get_unanswered_prompt_count(self, window_minutes: int = 30) -> int:
        """Get count of unanswered prompts in the time window."""
        cutoff = datetime.now() - timedelta(minutes=window_minutes)
        unanswered = [
            p for p in self._prompt_history
            if p.timestamp > cutoff and not p.was_answered
        ]
        return len(unanswered)
        
    def get_monitoring_interval(self) -> int:
        """Get the current monitoring interval in seconds."""
        return self.config.get_check_interval(self._current_state.level.name)
        
    def _close_distracting_tabs(self) -> None:
        """Close distracting application tabs/windows."""
        system = platform.system()
        
        try:
            if system == "Darwin":  # macOS
                # Use AppleScript to close distracting apps
                for app in self.config.aggressive_apps_to_close:
                    try:
                        # First try to close all windows
                        script = f'''
                        tell application "{app}"
                            close every window
                        end tell
                        '''
                        subprocess.run(
                            ["osascript", "-e", script],
                            check=False,
                            capture_output=True,
                            timeout=5
                        )
                    except Exception as e:
                        self.logger.error(f"Failed to close {app}: {e}")
                        
            elif system == "Windows":
                # Windows implementation would use taskkill or pywinauto
                self.logger.warning("Windows tab closing not yet implemented")
                
            elif system == "Linux":
                # Linux implementation would use wmctrl or xdotool
                self.logger.warning("Linux tab closing not yet implemented")
                
        except Exception as e:
            self.logger.error(f"Failed to close distracting tabs: {e}")
            
    def _lock_screen(self) -> None:
        """Lock the user's screen (extreme intervention)."""
        system = platform.system()
        
        try:
            if system == "Darwin":  # macOS
                # Lock screen on macOS
                subprocess.run(
                    ["pmset", "displaysleepnow"],
                    check=True
                )
            elif system == "Windows":
                # Lock screen on Windows
                subprocess.run(
                    ["rundll32.exe", "user32.dll,LockWorkStation"],
                    check=True
                )
            elif system == "Linux":
                # Try common Linux screen lock commands
                for cmd in [["gnome-screensaver-command", "-l"],
                          ["xdg-screensaver", "lock"],
                          ["xscreensaver-command", "-lock"]]:
                    try:
                        subprocess.run(cmd, check=True)
                        break
                    except:
                        continue
                        
        except Exception as e:
            self.logger.error(f"Failed to lock screen: {e}")
            
    def get_recent_unanswered_prompts(self, minutes: int = 30) -> List[PromptRecord]:
        """Get unanswered prompts from the specified time window."""
        cutoff = datetime.now() - timedelta(minutes=minutes)
        return [
            p for p in self._prompt_history
            if p.timestamp > cutoff and not p.was_answered
        ]
        
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about escalation state and history."""
        total_prompts = len(self._prompt_history)
        answered_prompts = sum(1 for p in self._prompt_history if p.was_answered)
        unanswered_prompts = total_prompts - answered_prompts
        
        # Calculate time in current level
        time_in_level = datetime.now() - self._current_state.started_at
        
        return {
            "current_level": self._current_state.level.name,
            "off_task_incidents": len(self._off_task_history),
            "total_prompts": total_prompts,
            "answered_prompts": answered_prompts,
            "unanswered_prompts": unanswered_prompts,
            "time_in_current_level": str(time_in_level),
            "prompts_at_current_level": self._current_state.prompt_count,
            "monitoring_interval": self.get_monitoring_interval(),
            "recent_off_task_count": len([
                x for x in self._off_task_history 
                if x[0] > datetime.now() - timedelta(hours=1)
            ])
        }
    
    def get_status_summary(self) -> Dict[str, Any]:
        """Get summary of current escalation status."""
        return {
            "current_level": self._current_state.level.name,
            "level_started": self._current_state.started_at.isoformat(),
            "prompts_at_level": self._current_state.prompt_count,
            "unanswered_prompts": self.get_unanswered_prompt_count(),
            "monitoring_interval": self.get_monitoring_interval(),
            "recent_off_task_count": len([
                x for x in self._off_task_history 
                if x[0] > datetime.now() - timedelta(hours=1)
            ])
        }


# Example usage and testing
if __name__ == "__main__":
    import asyncio
    
    async def main():
        """Demonstrate escalator functionality."""
        # Create escalator with custom config
        config = EscalatorConfig(
            prompts_before_escalation=2,  # Escalate faster for demo
            de_escalation_time_minutes=1  # De-escalate faster for demo
        )
        escalator = Escalator(config)
        
        print("=== Escalator Demo ===\n")
        
        # Simulate normal operation
        print("1. Starting in NORMAL mode")
        print(f"   State: {escalator.get_current_state().level.name}")
        print(f"   Monitoring interval: {escalator.get_monitoring_interval()}s\n")
        
        # Simulate off-task detection without justification
        print("2. User detected off-task (no justification)")
        analysis1 = AIAnalysisResult(
            on_task=False,
            confidence=0.9,
            reason="Browsing Reddit instead of coding",
            screenshot_ref="/screenshots/001.png"
        )
        escalator.process_analysis(analysis1)
        print(f"   State: {escalator.get_current_state().level.name}")
        print(f"   Prompts: {escalator.get_current_state().prompt_count}\n")
        
        # Record unanswered prompt
        request1 = JustificationRequest(
            timestamp=datetime.now(),
            detected_activity="Browsing Reddit",
            expected_task="Implement authentication module"
        )
        escalator.record_prompt(request1)
        
        # Another off-task detection
        print("3. User detected off-task again (no response)")
        analysis2 = AIAnalysisResult(
            on_task=False,
            confidence=0.85,
            reason="Still on Reddit",
            screenshot_ref="/screenshots/002.png"
        )
        escalator.process_analysis(analysis2)
        print(f"   State: {escalator.get_current_state().level.name}")
        print(f"   Should escalate: {escalator._current_state.prompt_count >= config.prompts_before_escalation}")
        
        # This should trigger escalation
        print("\n4. Third off-task detection triggers escalation")
        analysis3 = AIAnalysisResult(
            on_task=False,
            confidence=0.95,
            reason="YouTube open",
            screenshot_ref="/screenshots/003.png"
        )
        escalator.process_analysis(analysis3)
        print(f"   State: {escalator.get_current_state().level.name}")
        print(f"   Monitoring interval: {escalator.get_monitoring_interval()}s")
        print(f"   Intervention: {escalator.get_intervention_action().value}\n")
        
        # Test justification validation
        print("5. Testing justification validation")
        
        # Too short
        short_just = "Working"
        print(f"   '{short_just}' -> Valid: {escalator.check_justification(short_just)}")
        
        # Good justification
        good_just = "I'm researching authentication methods on Reddit for the auth module"
        print(f"   '{good_just}' -> Valid: {escalator.check_justification(good_just, 'Implement authentication module')}")
        
        # Simulate valid justification
        print("\n6. User provides valid justification")
        justification = JustificationResponse(
            user_input=good_just,
            valid=True,
            timestamp=datetime.now()
        )
        analysis4 = AIAnalysisResult(
            on_task=False,
            confidence=0.8,
            reason="Still browsing web",
            screenshot_ref="/screenshots/004.png"
        )
        escalator.process_analysis(analysis4, justification)
        print(f"   State still: {escalator.get_current_state().level.name} (no escalation due to valid justification)")
        
        # Test de-escalation
        print("\n7. User returns to task - testing de-escalation")
        # Simulate on-task behavior
        for i in range(3):
            analysis_on_task = AIAnalysisResult(
                on_task=True,
                confidence=0.95,
                reason="Coding in VS Code",
                screenshot_ref=f"/screenshots/on-task-{i}.png"
            )
            escalator.process_analysis(analysis_on_task)
            await asyncio.sleep(0.1)  # Simulate time passing
            
        # Force de-escalation for demo
        escalator._consecutive_on_task_start = datetime.now() - timedelta(minutes=2)
        escalator.process_analysis(analysis_on_task)
        print(f"   State after sustained on-task: {escalator.get_current_state().level.name}")
        
        # Show status summary
        print("\n8. Final Status Summary:")
        summary = escalator.get_status_summary()
        for key, value in summary.items():
            print(f"   {key}: {value}")
            
        print("\n=== Demo Complete ===")
        
    # Run the demo
    asyncio.run(main())
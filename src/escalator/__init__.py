"""Escalator module for handling escalation of off-task behavior interventions.

This module provides a progressive escalation system that monitors user behavior
and applies increasingly assertive interventions when off-task behavior persists.
"""

from .escalator import Escalator, InterventionAction, PromptRecord
from .config import EscalatorConfig

__all__ = [
    'Escalator',
    'EscalatorConfig', 
    'InterventionAction',
    'PromptRecord'
]
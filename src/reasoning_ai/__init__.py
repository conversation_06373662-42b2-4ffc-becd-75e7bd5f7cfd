"""Reasoning AI module for screenshot analysis and task alignment detection.

This module provides AI-powered analysis of screenshots to determine if user
activity aligns with their current task. Supports both mock mode for testing
and integration with OpenAI Vision API for production use.
"""

from .config import ReasoningConfig
from .reasoner import ReasoningAI

__all__ = ['ReasoningAI', 'ReasoningConfig']
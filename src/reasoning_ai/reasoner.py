"""AI Reasoning module for screenshot analysis.

Provides both mock and OpenAI Vision API implementations for analyzing
screenshots to determine if user activity aligns with current task.
"""

import asyncio
import base64
import json
import logging
import random
from pathlib import Path
from typing import Optional, Dict, Any

from ..interfaces import (
    AIReasoningInterface,
    AIAnalysisResult,
    ScreenshotData,
    TaskData
)
from .config import ReasoningConfig


class ReasoningAI(AIReasoningInterface):
    """AI reasoning implementation for task alignment analysis.
    
    Supports both mock mode (for testing) and real OpenAI Vision API integration.
    """
    
    def __init__(self, config: Optional[ReasoningConfig] = None):
        """Initialize the AI reasoner with configuration.
        
        Args:
            config: ReasoningConfig instance. If None, uses default config.
        """
        self.config = config or ReasoningConfig()
        self.logger = logging.getLogger(__name__)
        
        # Initialize OpenAI client if not in mock mode
        self._client = None
        if not self.config.mock_mode:
            try:
                import openai
                self._client = openai.OpenAI(api_key=self.config.openai_api_key)
                self.logger.info(f"Initialized OpenAI client with model {self.config.model_name}")
            except ImportError:
                self.logger.error("OpenAI package not installed. Run: pip install openai")
                raise
        
        # Mock behaviors for testing
        self._mock_behaviors = [
            {"on_task": True, "confidence": 0.95, "reason": "User actively coding in IDE"},
            {"on_task": True, "confidence": 0.90, "reason": "User reading documentation"},
            {"on_task": False, "confidence": 0.85, "reason": "Browsing social media"},
            {"on_task": False, "confidence": 0.80, "reason": "Watching YouTube videos"},
            {"on_task": True, "confidence": 0.88, "reason": "User in terminal running tests"},
            {"on_task": False, "confidence": 0.75, "reason": "Reading news websites"},
            {"on_task": True, "confidence": 0.92, "reason": "User in project management tool"},
            {"on_task": False, "confidence": 0.82, "reason": "Playing online game"},
            {"on_task": True, "confidence": 0.93, "reason": "Reviewing code on GitHub"},
            {"on_task": False, "confidence": 0.78, "reason": "Shopping online"},
        ]
    
    async def analyze_screenshot(
        self, 
        screenshot: ScreenshotData, 
        current_task: TaskData
    ) -> AIAnalysisResult:
        """Analyze screenshot to determine if user is on-task.
        
        Args:
            screenshot: Screenshot data including path to image file
            current_task: Current task the user should be working on
            
        Returns:
            AIAnalysisResult with on_task determination, confidence, and reasoning
        """
        if self.config.mock_mode:
            return await self._mock_analyze(screenshot, current_task)
        else:
            return await self._openai_analyze(screenshot, current_task)
    
    async def _mock_analyze(
        self,
        screenshot: ScreenshotData,
        current_task: TaskData
    ) -> AIAnalysisResult:
        """Mock analysis for testing without API calls."""
        # Simulate processing time
        await asyncio.sleep(0.5)
        
        # Select random behavior
        behavior = random.choice(self._mock_behaviors)
        
        # Build contextual reason
        if behavior["on_task"]:
            reason = f"{behavior['reason']} - working on '{current_task.description}'"
        else:
            reason = f"{behavior['reason']} instead of working on '{current_task.description}'"
        
        result = AIAnalysisResult(
            on_task=behavior["on_task"],
            confidence=behavior["confidence"],
            reason=reason,
            screenshot_ref=screenshot["path"]
        )
        
        self.logger.info(
            f"Mock Analysis: {'ON-TASK' if result.on_task else 'OFF-TASK'} "
            f"(confidence: {result.confidence:.2f})"
        )
        
        return result
    
    async def _openai_analyze(
        self,
        screenshot: ScreenshotData,
        current_task: TaskData
    ) -> AIAnalysisResult:
        """Analyze using OpenAI Vision API."""
        try:
            # Load and encode image
            image_path = Path(screenshot["path"])
            if not image_path.exists():
                raise FileNotFoundError(f"Screenshot not found: {image_path}")
            
            with open(image_path, "rb") as img_file:
                image_base64 = base64.b64encode(img_file.read()).decode("utf-8")
            
            # Prepare prompt
            prompt = self._build_analysis_prompt(current_task)
            
            # Call OpenAI Vision API
            response = await asyncio.to_thread(
                self._client.chat.completions.create,
                model=self.config.model_name,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
            )
            
            # Parse response
            return self._parse_openai_response(
                response.choices[0].message.content,
                screenshot["path"]
            )
            
        except Exception as e:
            self.logger.error(f"OpenAI analysis failed: {e}")
            # Fallback to mock on error
            return await self._mock_analyze(screenshot, current_task)
    
    def _build_analysis_prompt(self, task: TaskData) -> str:
        """Build prompt for OpenAI Vision API."""
        return f"""Analyze this screenshot to determine if the user is working on their current task.

Current task: "{task.description}"

Respond in JSON format with:
{{
    "on_task": true/false,
    "confidence": 0.0-1.0,
    "detected_activity": "what the user appears to be doing",
    "reasoning": "explanation of your determination"
}}

Consider:
- What applications are visible?
- What content is being viewed/edited?
- Does the activity relate to the task description?
- Be specific about what you observe.
"""
    
    def _parse_openai_response(self, response_text: str, screenshot_path: str) -> AIAnalysisResult:
        """Parse OpenAI response into AIAnalysisResult."""
        try:
            # Extract JSON from response
            data = json.loads(response_text)
            
            # Build reason from detected activity and reasoning
            reason = f"{data.get('detected_activity', 'Unknown activity')} - {data.get('reasoning', '')}"
            
            return AIAnalysisResult(
                on_task=data.get("on_task", False),
                confidence=float(data.get("confidence", 0.5)),
                reason=reason.strip(),
                screenshot_ref=screenshot_path
            )
            
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            self.logger.error(f"Failed to parse OpenAI response: {e}")
            # Return uncertain result on parse error
            return AIAnalysisResult(
                on_task=True,  # Err on the side of not interrupting
                confidence=0.5,
                reason="Unable to analyze screenshot - parsing error",
                screenshot_ref=screenshot_path
            )
    
    def set_confidence_threshold(self, threshold: float) -> None:
        """Set minimum confidence for off-task determination.
        
        Args:
            threshold: Confidence threshold between 0.0 and 1.0
            
        Raises:
            ValueError: If threshold is outside valid range
        """
        if not 0.0 <= threshold <= 1.0:
            raise ValueError("Confidence threshold must be between 0 and 1")
        self.config.confidence_threshold = threshold
        self.logger.info(f"Confidence threshold set to {threshold}")


# Example usage
if __name__ == "__main__":
    import asyncio
    from datetime import datetime
    
    async def main():
        """Example usage of ReasoningAI."""
        # Configure reasoning AI
        config = ReasoningConfig(
            mock_mode=True,  # Use mock mode for demo
            confidence_threshold=0.7
        )
        
        # Initialize reasoner
        reasoner = ReasoningAI(config)
        
        # Create sample screenshot data
        screenshot = ScreenshotData(
            path="/path/to/screenshot.png",
            timestamp=datetime.now().isoformat(),
            screen_id=1
        )
        
        # Test with different tasks
        tasks = [
            TaskData(
                id="task-001",
                priority=1,
                description="Implement user authentication module",
                status="in_progress",
                created_at=datetime.now()
            ),
            TaskData(
                id="task-002",
                priority=2,
                description="Write unit tests for API endpoints",
                status="pending",
                created_at=datetime.now()
            ),
            TaskData(
                id="task-003",
                priority=3,
                description="Update project documentation",
                status="pending",
                created_at=datetime.now()
            )
        ]
        
        print("ReasoningAI Example Usage\n" + "=" * 40)
        print(f"Mode: {'Mock' if config.mock_mode else 'OpenAI API'}")
        print(f"Confidence Threshold: {config.confidence_threshold}\n")
        
        # Analyze screenshot against different tasks
        for task in tasks:
            print(f"\nAnalyzing against task: {task.description}")
            result = await reasoner.analyze_screenshot(screenshot, task)
            
            print(f"  On-task: {'Yes' if result.on_task else 'No'}")
            print(f"  Confidence: {result.confidence:.2%}")
            print(f"  Reasoning: {result.reason}")
            
            if not result.on_task and result.confidence >= config.confidence_threshold:
                print("  -> User should be prompted for justification!")
        
        print("\n" + "=" * 40)
        print("\nTo use with real OpenAI API:")
        print("1. Set config.mock_mode = False")
        print("2. Provide config.openai_api_key")
        print("3. Ensure 'openai' package is installed")
    
    # Run the example
    asyncio.run(main())
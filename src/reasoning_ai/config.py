"""Configuration for Reasoning AI module."""

from dataclasses import dataclass
from typing import Optional


@dataclass
class ReasoningConfig:
    """Configuration settings for AI reasoning engine.
    
    Manages settings for OpenAI Vision API integration and analysis parameters.
    """
    # OpenAI API settings
    openai_api_key: Optional[str] = None
    model_name: str = "gpt-4-vision-preview"
    max_tokens: int = 500
    temperature: float = 0.3  # Lower for more consistent analysis
    
    # Analysis settings
    confidence_threshold: float = 0.7  # Minimum confidence for off-task determination
    analysis_interval: int = 5  # Seconds between analyses (main_ai_interval_one)
    
    # Mock mode for testing
    mock_mode: bool = True  # Set to False when using real OpenAI API
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        if not self.mock_mode and not self.openai_api_key:
            raise ValueError("OpenAI API key required when mock_mode is False")
        
        if not 0.0 <= self.confidence_threshold <= 1.0:
            raise ValueError("confidence_threshold must be between 0.0 and 1.0")
        
        if self.analysis_interval < 1:
            raise ValueError("analysis_interval must be at least 1 second")
"""Configuration for Task Manager module."""

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Final

__all__: Final = ["TaskManagerConfig"]


@dataclass(slots=True)
class TaskManagerConfig:
    """
    Configuration for task management.
    
    Attributes:
        tasks_file: Path to JSON file for task persistence
        auto_save: Whether to automatically save after changes
        default_priority: Default priority for new tasks (1-5, 1 being highest)
        output_dir: Base directory for output files
    """
    tasks_file: Path = Path("./output/tasks/tasks.json")
    auto_save: bool = True
    default_priority: int = 1
    output_dir: Path = Path("./output")
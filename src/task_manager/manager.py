"""Main implementation of the Task Manager."""

from __future__ import annotations

import json
import logging
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Optional

from ..interfaces import (
    TaskData,
    TaskManagerInterface,
    TaskPriority,
    TaskStatus,
    TaskNotFoundError,
)
from .config import TaskManagerConfig

__all__ = ["TaskManager"]

_LOG = logging.getLogger(__name__)


class TaskManager(TaskManagerInterface):
    """
    Manages task list operations and persistence.
    
    Implements the TaskManagerInterface to provide task management functionality
    with local JSON persistence.
    """
    
    def __init__(self, config: Optional[TaskManagerConfig] = None, storage_path: Optional[Path] = None) -> None:
        """
        Initialize task manager with configuration.
        
        Args:
            config: Configuration settings. Uses defaults if not provided.
            storage_path: Override path for tasks.json file.
        """
        self.config = config or TaskManagerConfig()
        if storage_path:
            self.config.tasks_file = Path(storage_path)
        self._tasks: List[TaskData] = []
        self._ensure_output_dir()
        self._load_tasks()
    
    def get_current_task(self) -> Optional[TaskData]:
        """
        Get the current highest priority incomplete task.
        
        Returns:
            TaskData for current task or None if no incomplete tasks
        """
        incomplete_tasks = [
            task for task in self._tasks
            if task.status in (TaskStatus.PENDING, TaskStatus.IN_PROGRESS)
        ]
        
        if not incomplete_tasks:
            return None
        
        # Sort by priority (lower number = higher priority)
        incomplete_tasks.sort(key=lambda t: t.priority.value)
        
        # If there's an IN_PROGRESS task, return it (even if not highest priority)
        in_progress = [t for t in incomplete_tasks if t.status == TaskStatus.IN_PROGRESS]
        if in_progress:
            return in_progress[0]
        
        # Otherwise return highest priority pending task
        return incomplete_tasks[0]
    
    def get_all_tasks(self) -> List[TaskData]:
        """
        Get all tasks ordered by priority.
        
        Returns:
            List of all tasks sorted by priority
        """
        return sorted(self._tasks, key=lambda t: t.priority.value)
    
    def add_task(self, description: str, priority: TaskPriority) -> TaskData:
        """
        Add a new task.
        
        Args:
            description: Human-readable task description
            priority: Task priority level
            
        Returns:
            The created TaskData
        """
        task = TaskData(
            id=f"task-{uuid.uuid4().hex[:8]}",
            priority=priority,
            description=description.strip(),
            status=TaskStatus.PENDING,
            created_at=datetime.now()
        )
        
        self._tasks.append(task)
        
        if self.config.auto_save:
            self._save_tasks()
        
        _LOG.info(f"Added task {task.id}: {description[:50]}...")
        return task
    
    def update_task_status(self, task_id: str, status: TaskStatus) -> TaskData:
        """
        Update task status.
        
        Args:
            task_id: ID of task to update
            status: New status
            
        Returns:
            Updated TaskData
            
        Raises:
            TaskNotFoundError: If task_id doesn't exist
        """
        task = self._get_task_by_id(task_id)
        if not task:
            raise TaskNotFoundError(f"Task {task_id} not found")
        
        old_status = task.status
        task.status = status
        
        # Set completed_at timestamp when marking as completed
        if status == TaskStatus.COMPLETED and task.completed_at is None:
            task.completed_at = datetime.now()
        
        if self.config.auto_save:
            self._save_tasks()
        
        _LOG.info(f"Updated task {task_id} status: {old_status.value} -> {status.value}")
        return task
    
    def import_tasks(self, task_list: List[str]) -> List[TaskData]:
        """
        Import tasks from a simple string list.
        
        Each string becomes a task description. Tasks are assigned priorities
        based on their order in the list (first = P1, second = P2, etc.).
        
        Args:
            task_list: List of task descriptions
            
        Returns:
            List of created TaskData objects
        """
        # Clear existing tasks
        self._tasks.clear()
        created_tasks = []
        
        for idx, description in enumerate(task_list):
            # Map index to priority (0->P1, 1->P2, etc.)
            # Cap at P5 for any index >= 4
            priority_value = min(idx + 1, 5)
            priority = TaskPriority(priority_value)
            
            task = self.add_task(description, priority)
            created_tasks.append(task)
        
        _LOG.info(f"Imported {len(created_tasks)} tasks")
        return created_tasks
    
    def update_task_order(self, task_id: str, new_priority: TaskPriority) -> TaskData:
        """
        Update task priority/order.
        
        Args:
            task_id: ID of task to update
            new_priority: New priority level
            
        Returns:
            Updated TaskData
            
        Raises:
            TaskNotFoundError: If task_id doesn't exist
        """
        task = self._get_task_by_id(task_id)
        if not task:
            raise TaskNotFoundError(f"Task {task_id} not found")
        
        old_priority = task.priority
        task.priority = new_priority
        
        if self.config.auto_save:
            self._save_tasks()
        
        _LOG.info(f"Updated task {task_id} priority: {old_priority.value} -> {new_priority.value}")
        return task
    
    def mark_complete(self, task_id: str) -> TaskData:
        """
        Mark a task as completed.
        
        Args:
            task_id: ID of task to complete
            
        Returns:
            Updated TaskData
            
        Raises:
            TaskNotFoundError: If task_id doesn't exist
        """
        return self.update_task_status(task_id, TaskStatus.COMPLETED)
    
    def _get_task_by_id(self, task_id: str) -> Optional[TaskData]:
        """Get task by ID."""
        for task in self._tasks:
            if task.id == task_id:
                return task
        return None
    
    def _ensure_output_dir(self) -> None:
        """Ensure output directory exists."""
        tasks_dir = self.config.tasks_file.parent
        tasks_dir.mkdir(parents=True, exist_ok=True)
    
    def _save_tasks(self) -> None:
        """
        Persist tasks to local storage.
        """
        try:
            data = {
                "tasks": [
                    {
                        "id": task.id,
                        "priority": task.priority.value,
                        "description": task.description,
                        "status": task.status.value,
                        "created_at": task.created_at.isoformat(),
                        "completed_at": task.completed_at.isoformat() if task.completed_at else None
                    }
                    for task in self._tasks
                ],
                "last_updated": datetime.now().isoformat()
            }
            
            with open(self.config.tasks_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            _LOG.debug(f"Saved {len(self._tasks)} tasks to {self.config.tasks_file}")
        except Exception as e:
            _LOG.error(f"Failed to save tasks: {e}")
    
    def _load_tasks(self) -> None:
        """
        Load tasks from local storage.
        """
        if not self.config.tasks_file.exists():
            _LOG.info("No existing tasks file found")
            return
        
        try:
            with open(self.config.tasks_file, 'r') as f:
                data = json.load(f)
            
            self._tasks.clear()
            
            for task_data in data.get("tasks", []):
                task = TaskData(
                    id=task_data["id"],
                    priority=TaskPriority(task_data["priority"]),
                    description=task_data["description"],
                    status=TaskStatus(task_data["status"]),
                    created_at=datetime.fromisoformat(task_data["created_at"]),
                    completed_at=datetime.fromisoformat(task_data["completed_at"]) 
                        if task_data.get("completed_at") else None
                )
                self._tasks.append(task)
            
            _LOG.info(f"Loaded {len(self._tasks)} tasks from {self.config.tasks_file}")
        except Exception as e:
            _LOG.error(f"Failed to load tasks: {e}")


# Example usage with __main__ guard
if __name__ == "__main__":
    # Configure logging for the example
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Initialize task manager
    config = TaskManagerConfig()
    manager = TaskManager(config)
    
    print("=== Task Manager Example Usage ===\n")
    
    # Import tasks from list
    print("1. Importing tasks from list...")
    task_descriptions = [
        "Complete PRD documentation",
        "Review code implementation",
        "Run integration tests",
        "Deploy to staging environment",
        "Update user documentation"
    ]
    
    imported_tasks = manager.import_tasks(task_descriptions)
    print(f"   Imported {len(imported_tasks)} tasks\n")
    
    # Get current priority task
    print("2. Getting current priority task...")
    current = manager.get_current_task()
    if current:
        print(f"   Current task: {current.description}")
        print(f"   Priority: P{current.priority.value}")
        print(f"   Status: {current.status.value}\n")
    
    # Mark task as in progress
    if current:
        print("3. Marking current task as in progress...")
        manager.update_task_status(current.id, TaskStatus.IN_PROGRESS)
        print(f"   Task {current.id} now in progress\n")
    
    # Mark task complete
    if current:
        print("4. Marking task as complete...")
        completed = manager.mark_complete(current.id)
        print(f"   Task completed at: {completed.completed_at}\n")
    
    # Show updated task list
    print("5. Current task list:")
    all_tasks = manager.get_all_tasks()
    for task in all_tasks:
        status_symbol = "✓" if task.status == TaskStatus.COMPLETED else "○"
        print(f"   {status_symbol} P{task.priority.value}: {task.description} [{task.status.value}]")
    
    # Get new current task after completion
    print("\n6. New current priority task:")
    new_current = manager.get_current_task()
    if new_current:
        print(f"   Current task: {new_current.description}")
        print(f"   Priority: P{new_current.priority.value}")
    else:
        print("   No pending tasks!")
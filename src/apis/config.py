"""Configuration for APIs module."""

from dataclasses import dataclass, field
from typing import Dict, Optional, List

@dataclass
class APIEndpoint:
    """Configuration for a single API endpoint."""
    name: str
    base_url: str
    api_key: Optional[str] = None
    headers: Dict[str, str] = field(default_factory=dict)
    timeout_seconds: int = 30
    retry_count: int = 3
    rate_limit_per_minute: Optional[int] = None

@dataclass
class APIConfig:
    """Configuration settings for API manager."""
    endpoints: Dict[str, APIEndpoint] = field(default_factory=dict)
    global_timeout: int = 60
    enable_caching: bool = True
    cache_ttl_seconds: int = 300
    enable_rate_limiting: bool = True
    log_requests: bool = True
    allowed_domains: List[str] = field(default_factory=list)
    
    def add_endpoint(self, endpoint: APIEndpoint):
        """Add an API endpoint configuration."""
        self.endpoints[endpoint.name] = endpoint
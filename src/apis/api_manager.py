"""Main implementation of the API Manager."""

import asyncio
import aiohttp
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
from urllib.parse import urljoin

from .config import APIConfig, APIEndpoint

@dataclass
class APIResponse:
    """Response from an API call."""
    status: int
    data: Any
    headers: Dict[str, str]
    elapsed_ms: float
    cached: bool = False

@dataclass
class RateLimitInfo:
    """Rate limit tracking information."""
    endpoint: str
    calls: List[datetime]
    limit_per_minute: int

class APIManager:
    """Manages API calls to external services."""
    
    def __init__(self, config: Optional[APIConfig] = None):
        self.config = config or APIConfig()
        self._session: Optional[aiohttp.ClientSession] = None
        self._cache: Dict[str, tuple[APIResponse, datetime]] = {}
        self._rate_limits: Dict[str, RateLimitInfo] = {}
        
    async def __aenter__(self):
        """Async context manager entry."""
        self._session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._session:
            await self._session.close()
            
    async def call(self, 
                   endpoint_name: str,
                   method: str = "GET",
                   path: str = "",
                   **kwargs) -> APIResponse:
        """Make an API call to a configured endpoint."""
        if endpoint_name not in self.config.endpoints:
            raise ValueError(f"Unknown endpoint: {endpoint_name}")
            
        endpoint = self.config.endpoints[endpoint_name]
        
        # Check rate limits
        if self.config.enable_rate_limiting and endpoint.rate_limit_per_minute:
            await self._check_rate_limit(endpoint_name, endpoint.rate_limit_per_minute)
            
        # Check cache
        cache_key = self._get_cache_key(endpoint_name, method, path, kwargs)
        if self.config.enable_caching and method == "GET":
            cached_response = self._get_cached_response(cache_key)
            if cached_response:
                return cached_response
                
        # Build request
        url = urljoin(endpoint.base_url, path)
        headers = endpoint.headers.copy()
        
        if endpoint.api_key:
            headers["Authorization"] = f"Bearer {endpoint.api_key}"
            
        # Make request
        start_time = datetime.now()
        
        async with self._get_session() as session:
            try:
                async with session.request(
                    method,
                    url,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=endpoint.timeout_seconds),
                    **kwargs
                ) as response:
                    data = await response.json() if response.content_type == "application/json" else await response.text()
                    
                    api_response = APIResponse(
                        status=response.status,
                        data=data,
                        headers=dict(response.headers),
                        elapsed_ms=(datetime.now() - start_time).total_seconds() * 1000
                    )
                    
                    # Cache successful GET responses
                    if self.config.enable_caching and method == "GET" and response.status == 200:
                        self._cache_response(cache_key, api_response)
                        
                    return api_response
                    
            except asyncio.TimeoutError:
                return APIResponse(
                    status=408,
                    data={"error": "Request timeout"},
                    headers={},
                    elapsed_ms=(datetime.now() - start_time).total_seconds() * 1000
                )
            except Exception as e:
                return APIResponse(
                    status=500,
                    data={"error": str(e)},
                    headers={},
                    elapsed_ms=(datetime.now() - start_time).total_seconds() * 1000
                )
                
    def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session."""
        if not self._session:
            self._session = aiohttp.ClientSession()
        return self._session
        
    async def _check_rate_limit(self, endpoint_name: str, limit: int):
        """Check and enforce rate limits."""
        now = datetime.now()
        
        if endpoint_name not in self._rate_limits:
            self._rate_limits[endpoint_name] = RateLimitInfo(
                endpoint=endpoint_name,
                calls=[],
                limit_per_minute=limit
            )
            
        rate_info = self._rate_limits[endpoint_name]
        
        # Remove calls older than 1 minute
        rate_info.calls = [call_time for call_time in rate_info.calls 
                          if now - call_time < timedelta(minutes=1)]
        
        # Check if limit exceeded
        if len(rate_info.calls) >= limit:
            sleep_time = 60 - (now - rate_info.calls[0]).total_seconds()
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
                
        rate_info.calls.append(now)
        
    def _get_cache_key(self, endpoint: str, method: str, path: str, kwargs: Dict) -> str:
        """Generate cache key for request."""
        key_parts = [endpoint, method, path]
        if kwargs:
            key_parts.append(json.dumps(kwargs, sort_keys=True))
        return ":".join(key_parts)
        
    def _get_cached_response(self, cache_key: str) -> Optional[APIResponse]:
        """Get response from cache if valid."""
        if cache_key in self._cache:
            response, cached_at = self._cache[cache_key]
            if datetime.now() - cached_at < timedelta(seconds=self.config.cache_ttl_seconds):
                response.cached = True
                return response
        return None
        
    def _cache_response(self, cache_key: str, response: APIResponse):
        """Cache an API response."""
        self._cache[cache_key] = (response, datetime.now())
        
    def clear_cache(self):
        """Clear the response cache."""
        self._cache.clear()
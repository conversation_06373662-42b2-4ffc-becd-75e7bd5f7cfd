"""Main implementation of the Local Store module."""

from __future__ import annotations

import gzip
import json
import logging
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

from ..interfaces import (
    AIAnalysisResult,
    JustificationResponse,
    LocalStorageInterface,
    ScreenshotData,
    TaskData,
)
from .config import LocalStoreConfig

# Set up module logger
_LOG = logging.getLogger(__name__)


class LocalStore(LocalStorageInterface):
    """Local storage implementation for the Productivity Guard system.
    
    This class implements all methods from LocalStorageInterface to provide:
    - Screenshot metadata storage with date-based organization
    - Task list persistence to JSON
    - Analysis and justification logging to JSONL files
    - Automatic directory creation
    - Optional compression for old data
    - Cleanup of data older than configured retention period
    
    Requirements implemented:
    - F-1: Persist task list to local store
    - F-5: Persist screenshots to dated folders
    - F-13: Store screenshots and logs locally only
    - N-2: Modular with TypeHinted interfaces
    - N-5: No outbound network calls
    """
    
    def __init__(self, config: Optional[LocalStoreConfig] = None, base_dir: Optional[Path] = None):
        """Initialize the local store with configuration.
        
        Args:
            config: Configuration for the local store. If None, uses defaults.
            base_dir: Override base directory for storage.
        """
        self.config = config or LocalStoreConfig()
        if base_dir:
            self.config.base_path = Path(base_dir)
            self.config.screenshots_path = self.config.base_path / "screenshots"
            self.config.logs_path = self.config.base_path / "logs"
        self._init_directories()
        _LOG.info(f"LocalStore initialized with base path: {self.config.base_path}")
    
    def save_screenshot_metadata(self, screenshot: ScreenshotData) -> None:
        """Save screenshot metadata to a JSON file in the same directory.
        
        This creates a companion .json file next to each screenshot with metadata.
        
        Args:
            screenshot: Screenshot data including path, timestamp, and screen_id
        """
        try:
            screenshot_path = Path(screenshot["path"])
            metadata_path = screenshot_path.with_suffix(".json")
            
            metadata = {
                "timestamp": screenshot["timestamp"],
                "screen_id": screenshot["screen_id"],
                "file_size": screenshot_path.stat().st_size if screenshot_path.exists() else 0,
                "saved_at": datetime.now().isoformat()
            }
            
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            _LOG.debug(f"Saved screenshot metadata: {metadata_path}")
            
        except Exception as e:
            _LOG.error(f"Failed to save screenshot metadata: {e}")
            raise
    
    def save_analysis_log(
        self,
        analysis: AIAnalysisResult,
        justification: Optional[JustificationResponse] = None
    ) -> None:
        """Log analysis result and any justification to JSONL file.
        
        Appends to a date-organized JSONL file for efficient append-only logging.
        
        Args:
            analysis: AI analysis result with on_task, confidence, reason, etc.
            justification: Optional user justification if off-task was detected
        """
        try:
            # Get date-based log folder
            log_folder = self._get_date_folder(self.config.logs_path)
            log_path = log_folder / self.config.analysis_file
            
            # Prepare log entry
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "on_task": analysis.on_task,
                "confidence": analysis.confidence,
                "reason": analysis.reason,
                "screenshot_ref": analysis.screenshot_ref,
            }
            
            # Add justification if provided
            if justification:
                log_entry["justification"] = {
                    "user_input": justification.user_input,
                    "valid": justification.valid,
                    "timestamp": justification.timestamp.isoformat()
                }
            
            # Append to JSONL file
            with open(log_path, 'a') as f:
                json.dump(log_entry, f)
                f.write('\n')
            
            _LOG.debug(f"Appended analysis log entry to {log_path}")
            
        except Exception as e:
            _LOG.error(f"Failed to save analysis log: {e}")
            raise
    
    def save_task_list(self, tasks: List[TaskData]) -> None:
        """Persist task list to JSON file.
        
        Saves the entire task list, overwriting previous version.
        
        Args:
            tasks: List of TaskData objects to save
        """
        try:
            tasks_path = self.config.base_path / self.config.tasks_file
            
            # Convert TaskData objects to dictionaries
            tasks_data = {
                "last_saved": datetime.now().isoformat(),
                "tasks": [
                    {
                        "id": task.id,
                        "priority": task.priority.value,
                        "description": task.description,
                        "status": task.status.value,
                        "created_at": task.created_at.isoformat(),
                        "completed_at": task.completed_at.isoformat() if task.completed_at else None
                    }
                    for task in tasks
                ]
            }
            
            # Save with pretty printing
            with open(tasks_path, 'w') as f:
                json.dump(tasks_data, f, indent=2)
            
            _LOG.info(f"Saved {len(tasks)} tasks to {tasks_path}")
            
        except Exception as e:
            _LOG.error(f"Failed to save task list: {e}")
            raise
    
    def load_task_list(self) -> List[TaskData]:
        """Load persisted task list from JSON file.
        
        Returns:
            List of TaskData objects, empty list if file doesn't exist
        """
        tasks_path = self.config.base_path / self.config.tasks_file
        
        if not tasks_path.exists():
            _LOG.info("No existing task file found")
            return []
        
        try:
            with open(tasks_path, 'r') as f:
                data = json.load(f)
            
            tasks = []
            for task_dict in data.get("tasks", []):
                # Import here to avoid circular imports
                from ..interfaces import TaskStatus, TaskPriority
                
                task = TaskData(
                    id=task_dict["id"],
                    priority=TaskPriority(task_dict["priority"]),
                    description=task_dict["description"],
                    status=TaskStatus(task_dict["status"]),
                    created_at=datetime.fromisoformat(task_dict["created_at"]),
                    completed_at=datetime.fromisoformat(task_dict["completed_at"]) 
                        if task_dict.get("completed_at") else None
                )
                tasks.append(task)
            
            _LOG.info(f"Loaded {len(tasks)} tasks from {tasks_path}")
            return tasks
            
        except Exception as e:
            _LOG.error(f"Failed to load task list: {e}")
            return []
    
    def get_activity_summary(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Get activity summary for date range.
        
        Aggregates data from analysis logs within the specified date range.
        
        Args:
            start_date: Start of date range (inclusive)
            end_date: End of date range (inclusive)
            
        Returns:
            Dictionary with summary statistics
        """
        summary = {
            "date_range": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat()
            },
            "total_analyses": 0,
            "on_task_count": 0,
            "off_task_count": 0,
            "average_confidence": 0.0,
            "justifications_provided": 0,
            "justifications_accepted": 0,
            "daily_breakdown": {}
        }
        
        total_confidence = 0.0
        current_date = start_date.date()
        end_date_only = end_date.date()
        
        # Iterate through each day in range
        while current_date <= end_date_only:
            date_str = current_date.strftime("%Y%b%d").upper()
            log_folder = self.config.logs_path / date_str
            log_path = log_folder / self.config.analysis_file
            
            if log_path.exists():
                daily_stats = {
                    "analyses": 0,
                    "on_task": 0,
                    "off_task": 0
                }
                
                try:
                    with open(log_path, 'r') as f:
                        for line in f:
                            if line.strip():
                                entry = json.loads(line)
                                entry_time = datetime.fromisoformat(entry["timestamp"])
                                
                                # Check if entry is within our time range
                                if start_date <= entry_time <= end_date:
                                    summary["total_analyses"] += 1
                                    daily_stats["analyses"] += 1
                                    
                                    if entry["on_task"]:
                                        summary["on_task_count"] += 1
                                        daily_stats["on_task"] += 1
                                    else:
                                        summary["off_task_count"] += 1
                                        daily_stats["off_task"] += 1
                                    
                                    total_confidence += entry["confidence"]
                                    
                                    if "justification" in entry:
                                        summary["justifications_provided"] += 1
                                        if entry["justification"]["valid"]:
                                            summary["justifications_accepted"] += 1
                
                    if daily_stats["analyses"] > 0:
                        summary["daily_breakdown"][date_str] = daily_stats
                        
                except Exception as e:
                    _LOG.error(f"Error reading log file {log_path}: {e}")
            
            current_date += timedelta(days=1)
        
        # Calculate average confidence
        if summary["total_analyses"] > 0:
            summary["average_confidence"] = total_confidence / summary["total_analyses"]
        
        return summary
    
    def save_justification_log(self, justification: JustificationResponse) -> None:
        """Save justification response to dedicated log file.
        
        Args:
            justification: User's justification response
        """
        try:
            log_folder = self._get_date_folder(self.config.logs_path)
            log_path = log_folder / self.config.justification_file
            
            log_entry = {
                "timestamp": justification.timestamp.isoformat(),
                "user_input": justification.user_input,
                "valid": justification.valid
            }
            
            with open(log_path, 'a') as f:
                json.dump(log_entry, f)
                f.write('\n')
            
            _LOG.debug(f"Saved justification to {log_path}")
            
        except Exception as e:
            _LOG.error(f"Failed to save justification log: {e}")
    
    def list_stored_items(
        self,
        item_type: str = "screenshots",
        date: Optional[datetime] = None
    ) -> List[Path]:
        """List stored items by type and optional date.
        
        Args:
            item_type: Type of items to list ("screenshots", "logs", "tasks")
            date: Optional date to filter by, defaults to today
            
        Returns:
            List of file paths matching the criteria
        """
        items = []
        
        if item_type == "tasks":
            tasks_path = self.config.base_path / self.config.tasks_file
            if tasks_path.exists():
                items.append(tasks_path)
        
        elif item_type == "screenshots":
            if date:
                date_folder = self.config.screenshots_path / date.strftime("%Y%b%d").upper()
            else:
                date_folder = self._get_date_folder(self.config.screenshots_path)
            
            if date_folder.exists():
                items.extend(sorted(date_folder.glob("*.png")))
                items.extend(sorted(date_folder.glob("*.png.gz")))  # Include compressed
        
        elif item_type == "logs":
            if date:
                date_folder = self.config.logs_path / date.strftime("%Y%b%d").upper()
            else:
                date_folder = self._get_date_folder(self.config.logs_path)
            
            if date_folder.exists():
                items.extend(sorted(date_folder.glob("*.jsonl")))
        
        return items
    
    def cleanup_old_data(self, days: Optional[int] = None) -> int:
        """Remove data older than specified days.
        
        Args:
            days: Number of days to keep, defaults to config.max_storage_days
            
        Returns:
            Number of items cleaned up
        """
        days = days or self.config.max_storage_days
        if days <= 0:
            return 0
        
        cutoff_date = datetime.now() - timedelta(days=days)
        removed_count = 0
        
        # Clean screenshots and logs
        for base_path in [self.config.screenshots_path, self.config.logs_path]:
            if not base_path.exists():
                continue
                
            for date_folder in base_path.iterdir():
                if not date_folder.is_dir():
                    continue
                
                try:
                    # Parse folder date
                    folder_date = datetime.strptime(date_folder.name, "%Y%b%d")
                    
                    if folder_date < cutoff_date:
                        shutil.rmtree(date_folder)
                        removed_count += 1
                        _LOG.info(f"Removed old folder: {date_folder}")
                        
                except ValueError:
                    # Skip folders that don't match date format
                    continue
        
        return removed_count
    
    def compress_old_screenshots(self, days_old: int = 7) -> int:
        """Compress screenshots older than specified days.
        
        Args:
            days_old: Age threshold for compression
            
        Returns:
            Number of files compressed
        """
        if not self.config.compression_enabled:
            return 0
        
        cutoff_date = datetime.now() - timedelta(days=days_old)
        compressed_count = 0
        
        for date_folder in self.config.screenshots_path.iterdir():
            if not date_folder.is_dir():
                continue
            
            try:
                folder_date = datetime.strptime(date_folder.name, "%Y%b%d")
                
                if folder_date < cutoff_date:
                    # Compress each PNG file
                    for img_path in date_folder.glob("*.png"):
                        compressed_path = img_path.with_suffix(".png.gz")
                        
                        if not compressed_path.exists():
                            with open(img_path, 'rb') as f_in:
                                with gzip.open(compressed_path, 'wb') as f_out:
                                    shutil.copyfileobj(f_in, f_out)
                            
                            # Remove original after successful compression
                            img_path.unlink()
                            compressed_count += 1
                            
                            _LOG.debug(f"Compressed: {img_path}")
                            
            except ValueError:
                continue
        
        if compressed_count > 0:
            _LOG.info(f"Compressed {compressed_count} old screenshots")
        
        return compressed_count
    
    def _init_directories(self) -> None:
        """Create required directories if they don't exist."""
        self.config.base_path.mkdir(parents=True, exist_ok=True)
        self.config.screenshots_path.mkdir(parents=True, exist_ok=True)
        self.config.logs_path.mkdir(parents=True, exist_ok=True)
        _LOG.debug("Initialized storage directories")
    
    def _get_date_folder(self, base_path: Path) -> Path:
        """Get or create today's date folder.
        
        Args:
            base_path: Base directory for date folders
            
        Returns:
            Path to date folder (created if doesn't exist)
        """
        today = datetime.now().strftime("%Y%b%d").upper()
        date_folder = base_path / today
        date_folder.mkdir(exist_ok=True)
        return date_folder


# Example usage with __main__ guard
if __name__ == "__main__":
    # Set up logging for demo
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create store with default config
    config = LocalStoreConfig()
    store = LocalStore(config)
    
    print("LocalStore Example Usage")
    print("=" * 50)
    
    # 1. Store screenshot metadata
    print("\n1. Storing screenshot metadata...")
    screenshot_data: ScreenshotData = {
        "path": "/tmp/test_screenshot.png",
        "timestamp": datetime.now().isoformat(),
        "screen_id": 1
    }
    
    # Create a dummy screenshot file for testing
    test_screenshot = Path("/tmp/test_screenshot.png")
    test_screenshot.write_bytes(b"dummy png data")
    
    store.save_screenshot_metadata(screenshot_data)
    print(f"   Saved metadata for: {screenshot_data['path']}")
    
    # 2. Store tasks
    print("\n2. Storing task list...")
    from ..interfaces import TaskData, TaskStatus, TaskPriority
    
    tasks = [
        TaskData(
            id="task-001",
            priority=TaskPriority.P1,
            description="Complete the LocalStore implementation",
            status=TaskStatus.IN_PROGRESS,
            created_at=datetime.now()
        ),
        TaskData(
            id="task-002",
            priority=TaskPriority.P2,
            description="Write unit tests for LocalStore",
            status=TaskStatus.PENDING,
            created_at=datetime.now()
        ),
        TaskData(
            id="task-003",
            priority=TaskPriority.P3,
            description="Update documentation",
            status=TaskStatus.COMPLETED,
            created_at=datetime.now() - timedelta(hours=2),
            completed_at=datetime.now() - timedelta(hours=1)
        )
    ]
    
    store.save_task_list(tasks)
    print(f"   Saved {len(tasks)} tasks")
    
    # 3. Load tasks back
    print("\n3. Loading tasks...")
    loaded_tasks = store.load_task_list()
    for task in loaded_tasks:
        print(f"   - [{task.status.value}] {task.description} (P{task.priority.value})")
    
    # 4. Store analysis logs
    print("\n4. Storing analysis logs...")
    analysis = AIAnalysisResult(
        on_task=False,
        confidence=0.85,
        reason="User browsing social media instead of coding",
        screenshot_ref=screenshot_data["path"]
    )
    
    # Store analysis without justification
    store.save_analysis_log(analysis)
    print("   Saved off-task analysis")
    
    # Store analysis with justification
    justification = JustificationResponse(
        user_input="Checking Twitter for API documentation updates",
        valid=True,
        timestamp=datetime.now()
    )
    
    store.save_analysis_log(analysis, justification)
    store.save_justification_log(justification)
    print("   Saved analysis with justification")
    
    # 5. List stored items
    print("\n5. Listing stored items...")
    
    screenshots = store.list_stored_items("screenshots")
    print(f"   Screenshots today: {len(screenshots)}")
    
    logs = store.list_stored_items("logs")
    print(f"   Log files today: {len(logs)}")
    
    task_files = store.list_stored_items("tasks")
    print(f"   Task files: {len(task_files)}")
    
    # 6. Get activity summary
    print("\n6. Getting activity summary...")
    summary = store.get_activity_summary(
        start_date=datetime.now() - timedelta(days=1),
        end_date=datetime.now()
    )
    
    print(f"   Total analyses: {summary['total_analyses']}")
    print(f"   On-task: {summary['on_task_count']}")
    print(f"   Off-task: {summary['off_task_count']}")
    print(f"   Average confidence: {summary['average_confidence']:.2f}")
    print(f"   Justifications provided: {summary['justifications_provided']}")
    
    # 7. Demonstrate compression (for old files)
    print("\n7. Compression example...")
    # Note: This won't compress anything since our files are new
    compressed = store.compress_old_screenshots(days_old=0)  # Compress immediately for demo
    print(f"   Compressed {compressed} screenshots")
    
    # 8. Cleanup example
    print("\n8. Cleanup example...")
    # Note: This won't remove anything since our files are new
    removed = store.cleanup_old_data(days=0)  # Clean immediately for demo
    print(f"   Removed {removed} old folders")
    
    print("\n" + "=" * 50)
    print("LocalStore example completed!")
    
    # Clean up test file
    test_screenshot.unlink(missing_ok=True)
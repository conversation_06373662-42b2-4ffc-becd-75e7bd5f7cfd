"""Configuration for Local Store module."""

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path

@dataclass
class LocalStoreConfig:
    """Configuration settings for local store.
    
    Implements requirement N-3 for configurable paths and settings.
    """
    # Base paths for different types of data
    base_path: Path = Path("./data")
    screenshots_path: Path = Path("./output/screenshots")
    logs_path: Path = Path("./output/logs")
    
    # File names for specific data types
    tasks_file: str = "tasks.json"
    analysis_file: str = "analysis_log.jsonl"
    justification_file: str = "justification_log.jsonl"
    
    # Feature flags
    compression_enabled: bool = True
    max_storage_days: int = 30  # Auto-cleanup after N days
    
    def __post_init__(self):
        """Ensure all paths are Path objects."""
        self.base_path = Path(self.base_path)
        self.screenshots_path = Path(self.screenshots_path)
        self.logs_path = Path(self.logs_path)
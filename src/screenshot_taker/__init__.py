# ── /src/screenshot_taker/__init__.py ───────────────────────────────────────────
from __future__ import annotations

import itertools
import logging
import signal
import threading
import time
from pathlib import Path
from typing import Final

from .config import ScreenshotConfig
from .taker import capture_once

__all__: Final = ["ScreenshotRunner"]

_LOG = logging.getLogger(__name__)

_STOP_EVENT = threading.Event()


class ScreenshotRunner:
    """
    Start and manage the periodic screenshot capture loop.

    Requirement: N-1, N-2
    Version: v01
    """

    def __init__(self, cfg: ScreenshotConfig) -> None:
        """
        Initialize runner with configuration.
      
        Requirement: N-2
        Version: v01
        """
        self.cfg = cfg
        self._thread: threading.Thread | None = None
        self._seq_gen = itertools.count(1)

    def start(self) -> None:
        """
        Start the background capture loop (non-blocking).

        Requirement: N-1
        Version: v01
        """
        if self._thread and self._thread.is_alive():
            _LOG.warning("Screenshot loop already running")
            return

        self._thread = threading.Thread(
            target=self._loop,
            name="ScreenshotLoop",
            daemon=True,
        )
        self._thread.start()
        _LOG.info(
            "Screenshot loop started (interval=%ss)",
            self.cfg.screenshot_interval,
        )

    def stop(self, timeout: float | None = 5.0) -> None:
        """
        Stop the capture loop gracefully.

        Requirement: N-1
        Version: v01
        """
        _STOP_EVENT.set()
        if self._thread:
            self._thread.join(timeout=timeout)
            _LOG.info("Screenshot loop stopped")
        _STOP_EVENT.clear()

    def _loop(self) -> None:
        """
        Main loop: capture, sleep, repeat until stopped.

        Requirement: N-1
        Version: v01
        """
        while not _STOP_EVENT.is_set():
            t0 = time.perf_counter()
            capture_once(self.cfg, self._seq_gen)
            elapsed = time.perf_counter() - t0
            sleep_for = max(
                0.0,
                self.cfg.screenshot_interval - elapsed,
            )
            time.sleep(sleep_for)


def _main() -> None:
    """
    CLI entrypoint for running the screenshot loop.

    Requirement: N-2
    Version: v01
    """
    import argparse

    parser = argparse.ArgumentParser(
        description="Run the Productivity-Guard screenshot loop"
    )
    parser.add_argument(
        "--interval",
        type=int,
        default=5,
        help="seconds between captures",
    )
    parser.add_argument(
        "--screens",
        type=int,
        nargs="+",
        default=[1],
        metavar="N",
        help=(
            "monitor indices to capture (1-based, as in macOS 'mss')"
        ),
    )
    parser.add_argument(
        "--out",
        type=Path,
        default=Path("./output/screenshots"),
    )
    parser.add_argument("--log-level", default="INFO")

    args = parser.parse_args()
    logging.basicConfig(
        level=args.log_level.upper(),
        format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    )

    cfg = ScreenshotConfig(
        screenshot_interval=max(1, args.interval),
        screens_to_capture=args.screens,
        output_root=args.out,
    )
    runner = ScreenshotRunner(cfg)

    def handle_sigint(*_: object) -> None:
        runner.stop()
        exit(0)
    
    signal.signal(signal.SIGINT, handle_sigint)
    runner.start()
    while True:
        time.sleep(1)


if __name__ == "__main__":
    _main()
# ── /src/screenshot_taker/taker.py ──────────────────────────────────────────────
from __future__ import annotations

import itertools
import logging
from datetime import datetime
from pathlib import Path
from typing import Iterator, Sequence

import mss
from PIL import Image

from .config import ScreenshotConfig

__all__: list[str] = ["ScreenshotTaker", "capture_once"]

_LOG = logging.getLogger(__name__)
_LOG.setLevel(logging.INFO)


def _todays_folder(root: Path) -> Path:
    """
    Create and return the folder for today's date.
  
    Requirement: F-5
    Version: v01
    """
    today = datetime.now().strftime("%Y%b%d").upper()
    folder = root / today
    folder.mkdir(parents=True, exist_ok=True)
    return folder


def _timestamp() -> str:
    """
    Generate a timestamp string for filenames (HHMMSS-µs).
  
    Requirement: F-5
    Version: v01
    """
    return datetime.now().strftime("%H%M%S-%f")


def _valid_monitors(requested: Sequence[int], total: int) -> list[int]:
    """
    Filter requested monitor indices to valid range.
  
    Requirement: F-4
    Version: v01
    """
    return [idx for idx in requested if 1 <= idx <= total]


def capture_once(cfg: ScreenshotConfig, seq_gen: Iterator[int] | None = None) -> None:
    """
    Capture screenshots for each configured monitor once.

    Requirement: F-4, F-5, N-4
    Version: v01
    """
    with mss.mss() as sct:
        total = len(sct.monitors) - 1  # index 0 = "all"
        targets = _valid_monitors(cfg.screens_to_capture, total)
        if not targets:
            _LOG.warning(
                "No valid screens to capture (requested=%s, total=%s)",
                cfg.screens_to_capture,
                total,
            )
            return

        folder = _todays_folder(cfg.output_root)
        seq_iter = seq_gen or itertools.count(1)

        for mon_idx in targets:
            try:
                src = sct.monitors[mon_idx]
                raw = sct.grab(src)  # raw BGRA bytes
                img = Image.frombytes("RGB", raw.size, raw.rgb)  # drop alpha

                filename = f"{_timestamp()}-{next(seq_iter):04d}.png"
                path = folder / filename
                img.save(path, "PNG")

                _LOG.debug("Saved screenshot: %s (monitor %s)", path, mon_idx)
            except Exception as exc:
                _LOG.warning(
                    "Capture failed for monitor %s: %s",
                    mon_idx,
                    exc,
                    exc_info=True,
                )


class ScreenshotTaker:
    """Screenshot capture implementation matching the interface."""
    
    def __init__(self, output_dir: Path, screens: list[int] | None = None):
        """Initialize the screenshot taker."""
        self.output_dir = Path(output_dir)
        self.screens = screens or [1, 2, 3, 4]
        self.logger = logging.getLogger(__name__)
        self._sequence_counter = itertools.count(1)
        
    async def capture_screenshot(self, screen_id: int) -> dict:
        """
        Capture a screenshot from the specified screen.
        
        Returns ScreenshotData compatible dict.
        """
        import asyncio
        
        # Run capture in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, 
            self._capture_screenshot_sync, 
            screen_id
        )
        
    def _capture_screenshot_sync(self, screen_id: int) -> dict:
        """Synchronous screenshot capture."""
        with mss.mss() as sct:
            total = len(sct.monitors) - 1
            
            if screen_id < 1 or screen_id > total:
                raise ValueError(f"Invalid screen_id {screen_id}, must be 1-{total}")
                
            folder = _todays_folder(self.output_dir)
            
            try:
                src = sct.monitors[screen_id]
                raw = sct.grab(src)
                img = Image.frombytes("RGB", raw.size, raw.rgb)
                
                timestamp = datetime.now()
                filename = f"{timestamp.strftime('%H%M%S-%f')}-{next(self._sequence_counter):04d}.png"
                path = folder / filename
                img.save(path, "PNG")
                
                return {
                    "path": str(path),
                    "timestamp": timestamp.isoformat(),
                    "screen_id": screen_id
                }
                
            except Exception as e:
                self.logger.error(f"Failed to capture screen {screen_id}: {e}")
                raise
                
    def get_available_screens(self) -> list[int]:
        """Get list of available screen IDs."""
        with mss.mss() as sct:
            # Exclude index 0 which is "all monitors"
            return list(range(1, len(sct.monitors)))
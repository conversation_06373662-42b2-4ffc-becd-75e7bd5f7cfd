# ── /src/screenshot_taker/config.py ─────────────────────────────────────────────
from __future__ import annotations

from dataclasses import dataclass, field
from pathlib import Path
from typing import Final, List


__all__: Final = ["ScreenshotConfig"]

@dataclass(slots=True)
class ScreenshotConfig:
    """
    Configuration for screenshot capture.
  
    Requirement: N-3
    Version: v01
    """
    screenshot_interval: int = 5                       # seconds
    screens_to_capture: List[int] = field(
        default_factory=lambda: [1]                    # "mss" uses 1-based indices
    )
    output_root: Path = Path("./output/screenshots")   # will be created if absent
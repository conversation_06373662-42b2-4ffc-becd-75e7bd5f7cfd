"""User interface implementation for Productivity Guard."""

from __future__ import annotations

import asyncio
import sys
import threading
from datetime import datetime
from typing import List, Optional

from ..interfaces import (
    JustificationRequest,
    JustificationResponse,
    TaskData,
    UserInterfaceInterface,
)
from .config import UIConfig

__all__ = ["UserInterface"]


class UserInterface(UserInterfaceInterface):
    """
    Terminal-based user interface implementation.
    
    Provides CLI commands for task management, justification prompts,
    and non-blocking notifications.
    """
    
    def __init__(self, config: Optional[UIConfig] = None, timeout: Optional[int] = None):
        """Initialize user interface with configuration."""
        self.config = config or UIConfig()
        if timeout is not None:
            self.config.prompt_timeout = timeout
        self._notification_queue: asyncio.Queue[tuple[str, str]] = asyncio.Queue()
        self._justification_event: Optional[asyncio.Event] = None
        self._justification_response: Optional[str] = None
        self._running = False
        self._input_thread: Optional[threading.Thread] = None
        
    async def request_justification(
        self, 
        request: JustificationRequest
    ) -> Optional[JustificationResponse]:
        """
        Request justification from user for off-task behavior.
        
        Args:
            request: Details about the detected off-task behavior
            
        Returns:
            JustificationResponse if user responds, None if timeout
        """
        # Format the prompt
        prompt_lines = [
            "",
            "=" * 60,
            "PRODUCTIVITY CHECK",
            "=" * 60,
            f"Time: {request.timestamp.strftime('%I:%M %p')}",
            f"",
            f"Detected Activity: {request.detected_activity}",
            f"Expected Task: {request.expected_task}",
            f"",
            "Please explain why you switched tasks:",
            ""
        ]
        
        # Display prompt
        for line in prompt_lines:
            print(line)
        
        # Set up event for response
        self._justification_event = asyncio.Event()
        self._justification_response = None
        
        # Start input thread if not running
        if not self._input_thread or not self._input_thread.is_alive():
            self._start_input_thread()
        
        try:
            # Wait for response with timeout
            await asyncio.wait_for(
                self._justification_event.wait(),
                timeout=self.config.prompt_timeout
            )
            
            if self._justification_response:
                # Validate the justification (simple length check for now)
                valid = len(self._justification_response.strip()) >= 10
                
                return JustificationResponse(
                    user_input=self._justification_response,
                    valid=valid,
                    timestamp=datetime.now()
                )
            
        except asyncio.TimeoutError:
            print("\nNo response received. Continuing monitoring...")
            
        finally:
            self._justification_event = None
            self._justification_response = None
            
        return None
    
    def send_notification(self, message: str, urgency: str = "normal") -> None:
        """
        Send notification to user.
        
        Args:
            message: Notification message
            urgency: Urgency level ("normal", "high")
        """
        # Add to queue for async processing
        try:
            self._notification_queue.put_nowait((message, urgency))
        except asyncio.QueueFull:
            # If queue is full, print directly
            self._display_notification(message, urgency)
    
    def _display_notification(self, message: str, urgency: str) -> None:
        """Display notification based on style and urgency."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if self.config.notification_style == "minimal":
            # Minimal style - just the message
            if urgency == "high":
                print(f"\n[!] {message}")
            else:
                print(f"\n[*] {message}")
        else:
            # Detailed style - with timestamp and formatting
            if urgency == "high":
                print(f"\n[{timestamp}] ⚠️  ALERT: {message}")
            else:
                print(f"\n[{timestamp}] ℹ️  {message}")
    
    def display_task_list(self, tasks: List[TaskData]) -> None:
        """Display formatted task list."""
        if not tasks:
            print("\nNo tasks found.")
            return
        
        print("\n" + "=" * 60)
        print("TASK LIST")
        print("=" * 60)
        
        for task in tasks:
            status_symbol = {
                "pending": "⏳",
                "in_progress": "🔄",
                "completed": "✅",
                "blocked": "🚫"
            }.get(task.status.value, "❓")
            
            priority_color = ""
            if self.config.cli_colors:
                # ANSI color codes for priorities
                priority_colors = {
                    1: "\033[91m",  # Red
                    2: "\033[93m",  # Yellow
                    3: "\033[92m",  # Green
                    4: "\033[94m",  # Blue
                    5: "\033[95m",  # Magenta
                }
                priority_color = priority_colors.get(task.priority.value, "")
                reset_color = "\033[0m"
            else:
                reset_color = ""
            
            print(f"{status_symbol} [{priority_color}P{task.priority.value}{reset_color}] "
                  f"{task.id}: {task.description}")
            
            if task.status.value == "completed" and task.completed_at:
                print(f"   Completed: {task.completed_at.strftime('%Y-%m-%d %H:%M')}")
        
        print("=" * 60)
    
    def handle_command(self, command: str) -> Optional[dict]:
        """
        Handle user commands for task management.
        
        Args:
            command: Command string to process
            
        Returns:
            Result dictionary with 'success' and 'message' keys
        """
        parts = command.strip().split()
        if not parts:
            return {"success": False, "message": "No command provided"}
        
        cmd = parts[0].lower()
        args = parts[1:]
        
        # Command handlers
        if cmd in ["help", "h", "?"]:
            return self._show_help()
        
        elif cmd in ["quit", "q", "exit"]:
            return {"success": True, "message": "Exiting..."}
        
        elif cmd == "clear":
            print("\033[2J\033[H")  # Clear screen
            return {"success": True, "message": "Screen cleared"}
        
        else:
            return {"success": False, "message": f"Unknown command: {cmd}"}
    
    def _show_help(self) -> dict:
        """Display help information."""
        help_text = """
Available Commands:
==================
Task Management:
  (Commands handled by TaskManager - not implemented here)

Interface Commands:
  help, h, ?     - Show this help message
  clear          - Clear the screen
  quit, q, exit  - Exit the program

Notifications:
  Notifications appear automatically when triggered by the system.

Justifications:
  When prompted, type your justification and press Enter.
  You have {} seconds to respond.
""".format(self.config.prompt_timeout)
        
        print(help_text)
        return {"success": True, "message": "Help displayed"}
    
    def _start_input_thread(self) -> None:
        """Start background thread for handling user input."""
        def input_handler():
            while self._running:
                try:
                    user_input = input()
                    
                    # Check if we're waiting for a justification
                    if self._justification_event and not self._justification_event.is_set():
                        self._justification_response = user_input
                        self._justification_event.set()
                    else:
                        # Regular command
                        result = self.handle_command(user_input)
                        if result:
                            if result.get("success"):
                                print(f"✓ {result.get('message', '')}")
                            else:
                                print(f"✗ {result.get('message', '')}")
                            
                            # Check for quit command
                            if user_input.strip().lower() in ["quit", "q", "exit"]:
                                self._running = False
                                break
                                
                except EOFError:
                    # Handle Ctrl+D
                    self._running = False
                    break
                except KeyboardInterrupt:
                    # Handle Ctrl+C
                    continue
        
        self._running = True
        self._input_thread = threading.Thread(
            target=input_handler,
            name="UserInputHandler",
            daemon=True
        )
        self._input_thread.start()
    
    async def run(self) -> None:
        """Run the user interface event loop."""
        self._running = True
        self._start_input_thread()
        
        print("Productivity Guard - User Interface")
        print("Type 'help' for available commands")
        print("-" * 40)
        
        try:
            while self._running:
                # Process notifications
                try:
                    message, urgency = await asyncio.wait_for(
                        self._notification_queue.get(),
                        timeout=0.1
                    )
                    self._display_notification(message, urgency)
                except asyncio.TimeoutError:
                    pass
                
                # Small delay to prevent busy waiting
                await asyncio.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\nShutting down...")
        finally:
            self._running = False


# Example usage with __main__ guard
if __name__ == "__main__":
    import asyncio
    from ..interfaces import TaskPriority, TaskStatus
    
    async def main():
        # Create UI instance
        ui_config = UIConfig(
            prompt_timeout=30,
            notification_style="detailed",
            cli_colors=True
        )
        ui = UserInterface(ui_config)
        
        # Example: Display notification
        ui.send_notification("Productivity Guard is starting...", "normal")
        ui.send_notification("High CPU usage detected!", "high")
        
        # Example: Request justification
        justification_request = JustificationRequest(
            timestamp=datetime.now(),
            detected_activity="Browsing Reddit",
            expected_task="Complete unit tests for auth module"
        )
        
        # Run justification request in background
        asyncio.create_task(test_justification(ui, justification_request))
        
        # Example: Show task list
        example_tasks = [
            TaskData(
                id="task-001",
                priority=TaskPriority.P1,
                description="Complete unit tests for auth module",
                status=TaskStatus.IN_PROGRESS,
                created_at=datetime.now()
            ),
            TaskData(
                id="task-002",
                priority=TaskPriority.P2,
                description="Review pull request #123",
                status=TaskStatus.PENDING,
                created_at=datetime.now()
            ),
            TaskData(
                id="task-003",
                priority=TaskPriority.P3,
                description="Update documentation",
                status=TaskStatus.COMPLETED,
                created_at=datetime.now(),
                completed_at=datetime.now()
            )
        ]
        
        ui.display_task_list(example_tasks)
        
        # Run the UI event loop
        await ui.run()
    
    async def test_justification(ui: UserInterface, request: JustificationRequest):
        """Test justification request after a delay."""
        await asyncio.sleep(5)
        print("\n" + "!" * 60)
        response = await ui.request_justification(request)
        if response:
            print(f"\nJustification received: {response.user_input}")
            print(f"Valid: {response.valid}")
        else:
            print("\nNo justification provided")
    
    # Run the example
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nExample terminated by user")
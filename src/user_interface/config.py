"""Configuration for user interface module."""

from __future__ import annotations

from dataclasses import dataclass
from typing import Literal

__all__ = ["UIConfig"]


@dataclass
class UIConfig:
    """
    Configuration for user interface.
    
    Attributes:
        prompt_timeout: Seconds to wait for user response to justification prompts
        notification_style: How to display notifications ("minimal" or "detailed")
        cli_colors: Whether to use ANSI colors in terminal output
    """
    prompt_timeout: int = 30  # seconds for user input timeout
    notification_style: Literal["minimal", "detailed"] = "detailed"
    cli_colors: bool = True
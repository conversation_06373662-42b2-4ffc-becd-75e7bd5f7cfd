# Productivity Guard - Integration Guide

This document describes the integrated Productivity Guard system and how to use it.

## Overview

The integrated system ties together all modules:
- **Screenshot Capture**: Takes periodic screenshots
- **Task Management**: Tracks and prioritizes tasks
- **AI Reasoning**: Analyzes screenshots to detect off-task behavior (currently mock)
- **User Interface**: Prompts for justifications
- **Escalation Manager**: Manages intervention levels
- **Local Storage**: Persists all data locally

## Quick Start

### 1. Run the Integration Test
```bash
python test_integration.py
```
This runs a 15-second test of the complete system.

### 2. Run the Example Workflow
```bash
python example_workflow.py
```
This demonstrates a full workflow including task creation and off-task detection.

### 3. Run the Full Application
```bash
python main.py
```
The main.py is configured to use the integrated implementation by default.

## Architecture

### Main Integration File: `/src/main_app.py`

The `ProductivityGuardApp` class orchestrates all components:

```python
class ProductivityGuardApp:
    def __init__(self, config: SystemConfig):
        # Initializes all components
        self.screenshot_taker = ScreenshotTaker(...)
        self.task_manager = TaskManager(...)
        self.ai_reasoner = AIReasoner(...)
        self.user_interface = UserInterface(...)
        self.escalation_manager = EscalationManager(...)
        self.local_store = LocalStore(...)
```

### Data Flow

1. **Screenshot Loop** (runs every 5 seconds):
   - Captures screenshots from configured screens
   - Saves to dated folders
   - Stores metadata

2. **AI Analysis Loop** (interval based on escalation):
   - Gets current priority task
   - Analyzes most recent screenshot
   - Determines if user is on-task
   - Logs results

3. **Off-Task Handling**:
   - Requests justification from user
   - Processes escalation state
   - Executes interventions if needed

### Configuration

Configure the system via `SystemConfig`:

```python
config = SystemConfig(
    screenshot_interval=5,         # seconds between screenshots
    main_ai_interval_one=5,       # normal AI check interval
    main_ai_interval_two=1,       # escalated AI check interval
    screens_to_capture=[1,2,3,4], # screen IDs to capture
    justification_timeout=60,     # seconds to wait for response
    escalation_prompt_threshold=3,# prompts before escalating
    output_dir=Path("./output")   # where to store data
)
```

## Components

### Screenshot Taker
- Captures screenshots asynchronously
- Supports multiple screens
- Saves to date-organized folders

### Task Manager
- CRUD operations for tasks
- Priority-based task selection
- JSON persistence

### AI Reasoner (Mock)
- Simulates AI analysis with realistic behaviors
- Returns on-task/off-task decisions with confidence
- Ready for real AI integration

### User Interface
- Terminal-based interaction
- Non-blocking justification prompts
- Notification system

### Escalation Manager
- Tracks off-task incidents
- Manages escalation levels:
  - NORMAL: Regular monitoring
  - WARNING: First warning issued
  - FREQUENT: Increased monitoring
  - AGGRESSIVE: App closing intervention

### Local Store
- Screenshot metadata storage
- Task list persistence
- Analysis logging (JSONL format)
- Activity summaries

## Output Structure

```
output/
├── screenshots/
│   └── 2025JUN15/          # Date folders
│       ├── 001234-567890-0001.png
│       └── *.json          # Metadata files
├── logs/
│   └── 2025JUN15/
│       ├── analysis_log.jsonl
│       └── justification_log.jsonl
└── tasks.json              # Task list
```

## Logging

The system uses Python's logging module extensively:
- Console output for important events
- File logging to `productivity_guard.log`
- Component-specific loggers

## Mock Components

The current implementation includes mock components for testing:
- **AIReasoner**: Returns random but realistic on/off-task decisions
- **UserInterface**: Auto-responds to prompts in example mode

These can be replaced with real implementations without changing the integration.

## Error Handling

- Graceful shutdown on Ctrl+C
- Component failures don't crash the system
- All exceptions are logged
- Automatic retry for transient failures

## Next Steps

1. **Replace Mock AI**: Integrate with Claude, GPT, or local LLM
2. **Enhanced UI**: Add GUI or web interface
3. **Real-time Dashboard**: Visualize productivity metrics
4. **Advanced Interventions**: More sophisticated app management
5. **Analytics**: Detailed productivity reports

## Testing

Run the test suite:
```bash
python -m pytest tests/
```

Individual component testing:
```bash
python -m src.screenshot_taker
python -m src.task_manager.manager
python -m src.user_interface.interface
```

## Troubleshooting

### Screenshots not capturing
- Check screen permissions (especially on macOS)
- Verify screen IDs with `ScreenshotTaker.get_available_screens()`

### AI analysis not running
- Ensure tasks exist (`task_manager.get_all_tasks()`)
- Check screenshots are being saved
- Review logs for errors

### Escalation not working
- Verify justification timeout isn't too long
- Check escalation thresholds
- Review escalation state in logs
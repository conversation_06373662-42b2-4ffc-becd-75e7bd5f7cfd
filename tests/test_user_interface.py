"""
Comprehensive tests for the User Interface module.

Tests cover:
- Justification prompts and responses
- Notification display
- Command handling
- Task list display
- Async operations
- Timeout handling
- Error conditions
"""

import asyncio
import sys
from datetime import datetime, timed<PERSON>ta
from io import String<PERSON>
from typing import List
from unittest.mock import Mock, patch, AsyncMock, MagicMock

import pytest

from src.interfaces import (
    JustificationRequest,
    JustificationResponse,
    TaskData,
    TaskPriority,
    TaskStatus,
)
from src.user_interface.config import UIConfig
from src.user_interface.interface import UserInterface
from tests.test_utils import (
    async_return,
    generate_tasks,
    wait_for_condition,
)


class TestUserInterface:
    """Test suite for UserInterface class."""
    
    # ─────────────────────────────────────────────────────────────────────────
    # Initialization Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_init_default_config(self):
        """Test initialization with default configuration."""
        ui = UserInterface()
        
        assert ui.config is not None
        assert isinstance(ui.config, UIConfig)
        assert ui._notification_queue is not None
        assert ui._justification_event is None
        assert ui._justification_response is None
        assert ui._running is False
    
    def test_init_custom_config(self):
        """Test initialization with custom configuration."""
        config = UIConfig(
            prompt_timeout=60,
            notification_style="minimal",
            cli_colors=False
        )
        ui = UserInterface(config=config)
        
        assert ui.config == config
        assert ui.config.prompt_timeout == 60
        assert ui.config.notification_style == "minimal"
        assert ui.config.cli_colors is False
    
    def test_init_timeout_override(self):
        """Test initialization with timeout override."""
        config = UIConfig(prompt_timeout=30)
        ui = UserInterface(config=config, timeout=45)
        
        assert ui.config.prompt_timeout == 45  # Override should take precedence
    
    # ─────────────────────────────────────────────────────────────────────────
    # Justification Request Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    @pytest.mark.asyncio
    async def test_request_justification_success(self, capsys):
        """Test successful justification request."""
        ui = UserInterface(timeout=5)
        
        request = JustificationRequest(
            timestamp=datetime.now(),
            detected_activity="Browsing Reddit",
            expected_task="Complete unit tests"
        )
        
        # Simulate user input
        async def provide_input():
            await asyncio.sleep(0.1)  # Small delay
            ui._justification_response = "Looking up testing best practices"
            if ui._justification_event:
                ui._justification_event.set()
        
        # Run request and input provision concurrently
        input_task = asyncio.create_task(provide_input())
        response = await ui.request_justification(request)
        await input_task
        
        # Verify response
        assert response is not None
        assert response.user_input == "Looking up testing best practices"
        assert response.valid is True  # Length >= 10
        assert isinstance(response.timestamp, datetime)
        
        # Check output
        captured = capsys.readouterr()
        assert "PRODUCTIVITY CHECK" in captured.out
        assert "Browsing Reddit" in captured.out
        assert "Complete unit tests" in captured.out
    
    @pytest.mark.asyncio
    async def test_request_justification_short_response(self):
        """Test justification with response too short to be valid."""
        ui = UserInterface(timeout=5)
        
        request = JustificationRequest(
            timestamp=datetime.now(),
            detected_activity="Gaming",
            expected_task="Code review"
        )
        
        # Simulate short user input
        async def provide_input():
            await asyncio.sleep(0.1)
            ui._justification_response = "Break"  # Too short
            if ui._justification_event:
                ui._justification_event.set()
        
        input_task = asyncio.create_task(provide_input())
        response = await ui.request_justification(request)
        await input_task
        
        assert response is not None
        assert response.user_input == "Break"
        assert response.valid is False  # Length < 10
    
    @pytest.mark.asyncio
    async def test_request_justification_timeout(self, capsys):
        """Test justification request timeout."""
        ui = UserInterface(timeout=0.1)  # Very short timeout
        
        request = JustificationRequest(
            timestamp=datetime.now(),
            detected_activity="Social media",
            expected_task="Documentation"
        )
        
        # Don't provide any input
        response = await ui.request_justification(request)
        
        assert response is None
        
        # Check timeout message
        captured = capsys.readouterr()
        assert "No response received" in captured.out
    
    @pytest.mark.asyncio
    async def test_request_justification_empty_response(self):
        """Test justification with empty/whitespace response."""
        ui = UserInterface(timeout=5)
        
        request = JustificationRequest(
            timestamp=datetime.now(),
            detected_activity="YouTube",
            expected_task="Bug fixes"
        )
        
        # Simulate empty input
        async def provide_input():
            await asyncio.sleep(0.1)
            ui._justification_response = "   "  # Just whitespace
            if ui._justification_event:
                ui._justification_event.set()
        
        input_task = asyncio.create_task(provide_input())
        response = await ui.request_justification(request)
        await input_task
        
        assert response is not None
        assert response.valid is False  # Stripped length is 0
    
    # ─────────────────────────────────────────────────────────────────────────
    # Notification Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_send_notification_normal(self, capsys):
        """Test sending normal notification."""
        ui = UserInterface()
        
        ui.send_notification("Task completed successfully")
        
        # Process the queue manually
        message, urgency = ui._notification_queue.get_nowait()
        ui._display_notification(message, urgency)
        
        captured = capsys.readouterr()
        assert "Task completed successfully" in captured.out
        assert "[*]" in captured.out or "ℹ️" in captured.out
    
    def test_send_notification_high_urgency(self, capsys):
        """Test sending high urgency notification."""
        ui = UserInterface()
        
        ui.send_notification("Critical: System overload!", urgency="high")
        
        # Process the queue manually
        message, urgency = ui._notification_queue.get_nowait()
        ui._display_notification(message, urgency)
        
        captured = capsys.readouterr()
        assert "Critical: System overload!" in captured.out
        assert "[!]" in captured.out or "⚠️" in captured.out
    
    def test_send_notification_minimal_style(self, capsys):
        """Test notifications with minimal style."""
        config = UIConfig(notification_style="minimal")
        ui = UserInterface(config=config)
        
        ui._display_notification("Test message", "normal")
        ui._display_notification("Urgent message", "high")
        
        captured = capsys.readouterr()
        assert "[*] Test message" in captured.out
        assert "[!] Urgent message" in captured.out
    
    def test_send_notification_detailed_style(self, capsys):
        """Test notifications with detailed style."""
        config = UIConfig(notification_style="detailed")
        ui = UserInterface(config=config)
        
        with patch('datetime.datetime') as mock_datetime:
            mock_datetime.now.return_value.strftime.return_value = "12:34:56"
            
            ui._display_notification("Test message", "normal")
            ui._display_notification("Urgent message", "high")
        
        captured = capsys.readouterr()
        assert "[12:34:56] ℹ️  Test message" in captured.out
        assert "[12:34:56] ⚠️  ALERT: Urgent message" in captured.out
    
    def test_send_notification_queue_full(self, capsys):
        """Test behavior when notification queue is full."""
        ui = UserInterface()
        
        # Fill the queue
        with patch.object(ui._notification_queue, 'put_nowait', side_effect=asyncio.QueueFull):
            ui.send_notification("Overflow message")
        
        # Should print directly when queue is full
        captured = capsys.readouterr()
        assert "Overflow message" in captured.out
    
    # ─────────────────────────────────────────────────────────────────────────
    # Task List Display Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_display_task_list_empty(self, capsys):
        """Test displaying empty task list."""
        ui = UserInterface()
        
        ui.display_task_list([])
        
        captured = capsys.readouterr()
        assert "No tasks found" in captured.out
    
    def test_display_task_list_basic(self, capsys, sample_tasks):
        """Test displaying basic task list."""
        ui = UserInterface()
        
        ui.display_task_list(sample_tasks)
        
        captured = capsys.readouterr()
        assert "TASK LIST" in captured.out
        assert "=" * 60 in captured.out
        
        # Check task details
        for task in sample_tasks:
            assert task.id in captured.out
            assert task.description in captured.out
            assert f"P{task.priority.value}" in captured.out
    
    def test_display_task_list_with_colors(self, capsys):
        """Test displaying task list with colors enabled."""
        config = UIConfig(cli_colors=True)
        ui = UserInterface(config=config)
        
        tasks = [
            TaskData(
                id="task-001",
                priority=TaskPriority.P1,
                description="High priority task",
                status=TaskStatus.PENDING,
                created_at=datetime.now()
            ),
            TaskData(
                id="task-002",
                priority=TaskPriority.P3,
                description="Medium priority task",
                status=TaskStatus.IN_PROGRESS,
                created_at=datetime.now()
            )
        ]
        
        ui.display_task_list(tasks)
        
        captured = capsys.readouterr()
        # Check for ANSI color codes
        assert "\033[91m" in captured.out  # Red for P1
        assert "\033[92m" in captured.out  # Green for P3
        assert "\033[0m" in captured.out   # Reset color
    
    def test_display_task_list_status_symbols(self, capsys):
        """Test task list displays correct status symbols."""
        ui = UserInterface()
        
        tasks = [
            TaskData(id="t1", priority=TaskPriority.P1, description="Pending",
                    status=TaskStatus.PENDING, created_at=datetime.now()),
            TaskData(id="t2", priority=TaskPriority.P2, description="In Progress",
                    status=TaskStatus.IN_PROGRESS, created_at=datetime.now()),
            TaskData(id="t3", priority=TaskPriority.P3, description="Completed",
                    status=TaskStatus.COMPLETED, created_at=datetime.now(),
                    completed_at=datetime.now()),
            TaskData(id="t4", priority=TaskPriority.P4, description="Blocked",
                    status=TaskStatus.BLOCKED, created_at=datetime.now()),
        ]
        
        ui.display_task_list(tasks)
        
        captured = capsys.readouterr()
        assert "⏳" in captured.out  # Pending
        assert "🔄" in captured.out  # In progress
        assert "✅" in captured.out  # Completed
        assert "🚫" in captured.out  # Blocked
    
    def test_display_task_list_completed_timestamp(self, capsys):
        """Test completed tasks show completion timestamp."""
        ui = UserInterface()
        
        completed_time = datetime(2025, 6, 15, 14, 30)
        task = TaskData(
            id="task-001",
            priority=TaskPriority.P1,
            description="Completed task",
            status=TaskStatus.COMPLETED,
            created_at=datetime.now(),
            completed_at=completed_time
        )
        
        ui.display_task_list([task])
        
        captured = capsys.readouterr()
        assert "Completed: 2025-06-15 14:30" in captured.out
    
    # ─────────────────────────────────────────────────────────────────────────
    # Command Handling Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_handle_command_help(self, capsys):
        """Test help command."""
        ui = UserInterface()
        
        for cmd in ["help", "h", "?"]:
            result = ui.handle_command(cmd)
            assert result["success"] is True
            assert "Help displayed" in result["message"]
        
        captured = capsys.readouterr()
        assert "Available Commands" in captured.out
        assert "Task Management" in captured.out
        assert "Interface Commands" in captured.out
    
    def test_handle_command_quit(self):
        """Test quit commands."""
        ui = UserInterface()
        
        for cmd in ["quit", "q", "exit"]:
            result = ui.handle_command(cmd)
            assert result["success"] is True
            assert "Exiting" in result["message"]
    
    def test_handle_command_clear(self, capsys):
        """Test clear command."""
        ui = UserInterface()
        
        result = ui.handle_command("clear")
        
        assert result["success"] is True
        assert "Screen cleared" in result["message"]
        
        captured = capsys.readouterr()
        # Clear screen escape sequence
        assert "\033[2J\033[H" in captured.out
    
    def test_handle_command_unknown(self):
        """Test unknown command handling."""
        ui = UserInterface()
        
        result = ui.handle_command("foobar")
        
        assert result["success"] is False
        assert "Unknown command: foobar" in result["message"]
    
    def test_handle_command_empty(self):
        """Test empty command handling."""
        ui = UserInterface()
        
        result = ui.handle_command("")
        
        assert result["success"] is False
        assert "No command provided" in result["message"]
    
    def test_handle_command_whitespace(self):
        """Test whitespace-only command."""
        ui = UserInterface()
        
        result = ui.handle_command("   ")
        
        assert result["success"] is False
        assert "No command provided" in result["message"]
    
    # ─────────────────────────────────────────────────────────────────────────
    # Input Thread Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_start_input_thread(self):
        """Test input thread starts correctly."""
        ui = UserInterface()
        
        ui._start_input_thread()
        
        assert ui._running is True
        assert ui._input_thread is not None
        assert ui._input_thread.is_alive()
        assert ui._input_thread.daemon is True
        
        # Cleanup
        ui._running = False
        ui._input_thread.join(timeout=1)
    
    @patch('builtins.input', side_effect=["help", "quit"])
    def test_input_thread_commands(self, mock_input, capsys):
        """Test input thread processes commands."""
        ui = UserInterface()
        
        ui._start_input_thread()
        
        # Give thread time to process
        import time
        time.sleep(0.5)
        
        assert ui._running is False  # Should stop after "quit"
        
        captured = capsys.readouterr()
        assert "✓" in captured.out  # Success markers
    
    @patch('builtins.input', side_effect=EOFError)
    def test_input_thread_eof(self, mock_input):
        """Test input thread handles EOF (Ctrl+D)."""
        ui = UserInterface()
        
        ui._start_input_thread()
        
        # Give thread time to process
        import time
        time.sleep(0.2)
        
        assert ui._running is False
    
    @patch('builtins.input', side_effect=KeyboardInterrupt)
    def test_input_thread_keyboard_interrupt(self, mock_input):
        """Test input thread handles keyboard interrupt."""
        ui = UserInterface()
        
        ui._start_input_thread()
        
        # Should continue running after KeyboardInterrupt
        import time
        time.sleep(0.2)
        
        assert ui._running is True
        
        # Cleanup
        ui._running = False
    
    # ─────────────────────────────────────────────────────────────────────────
    # Async Run Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    @pytest.mark.asyncio
    async def test_run_basic(self, capsys):
        """Test basic run loop."""
        ui = UserInterface()
        
        # Run briefly then stop
        async def stop_after_delay():
            await asyncio.sleep(0.3)
            ui._running = False
        
        await asyncio.gather(
            ui.run(),
            stop_after_delay()
        )
        
        captured = capsys.readouterr()
        assert "Productivity Guard - User Interface" in captured.out
        assert "Type 'help' for available commands" in captured.out
    
    @pytest.mark.asyncio
    async def test_run_processes_notifications(self, capsys):
        """Test run loop processes notifications."""
        ui = UserInterface()
        
        # Add notifications to queue
        ui.send_notification("Test notification 1")
        ui.send_notification("Test notification 2", urgency="high")
        
        # Run briefly
        async def stop_after_delay():
            await asyncio.sleep(0.3)
            ui._running = False
        
        await asyncio.gather(
            ui.run(),
            stop_after_delay()
        )
        
        captured = capsys.readouterr()
        assert "Test notification 1" in captured.out
        assert "Test notification 2" in captured.out
    
    @pytest.mark.asyncio
    async def test_run_exception_handling(self):
        """Test run loop handles exceptions gracefully."""
        ui = UserInterface()
        
        # Mock notification processing to raise exception
        with patch.object(ui, '_display_notification', side_effect=Exception("Test error")):
            ui.send_notification("Will cause error")
            
            # Should not crash
            async def stop_after_delay():
                await asyncio.sleep(0.2)
                ui._running = False
            
            # This should complete without raising
            await asyncio.gather(
                ui.run(),
                stop_after_delay()
            )


# ─────────────────────────────────────────────────────────────────────────────
# Integration Tests
# ─────────────────────────────────────────────────────────────────────────────


class TestUserInterfaceIntegration:
    """Integration tests for UserInterface."""
    
    @pytest.mark.asyncio
    async def test_justification_flow_with_input_thread(self):
        """Test complete justification flow with input thread."""
        ui = UserInterface(timeout=5)
        
        # Start input thread
        ui._start_input_thread()
        
        request = JustificationRequest(
            timestamp=datetime.now(),
            detected_activity="Checking email",
            expected_task="Code review"
        )
        
        # Simulate user typing justification
        with patch('builtins.input', return_value="Reviewing code-related emails from team"):
            response = await ui.request_justification(request)
        
        assert response is not None
        assert response.valid is True
        assert "code-related emails" in response.user_input
        
        # Cleanup
        ui._running = False
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self):
        """Test UI handles concurrent operations."""
        ui = UserInterface()
        
        # Multiple concurrent operations
        async def send_notifications():
            for i in range(10):
                ui.send_notification(f"Notification {i}")
                await asyncio.sleep(0.05)
        
        async def display_tasks():
            tasks = generate_tasks(5)
            for _ in range(3):
                ui.display_task_list(tasks)
                await asyncio.sleep(0.1)
        
        async def handle_commands():
            for cmd in ["help", "clear", "unknown"]:
                ui.handle_command(cmd)
                await asyncio.sleep(0.08)
        
        # Run all concurrently
        await asyncio.gather(
            send_notifications(),
            display_tasks(),
            handle_commands()
        )
        
        # Should complete without errors
        assert True
    
    @pytest.mark.asyncio
    async def test_notification_during_justification(self, capsys):
        """Test notifications work during justification prompt."""
        ui = UserInterface(timeout=2)
        
        request = JustificationRequest(
            timestamp=datetime.now(),
            detected_activity="Gaming",
            expected_task="Testing"
        )
        
        # Send notification during justification
        async def send_notification_delayed():
            await asyncio.sleep(0.5)
            ui.send_notification("Urgent: System alert!", urgency="high")
        
        # Let justification timeout
        notification_task = asyncio.create_task(send_notification_delayed())
        response = await ui.request_justification(request)
        await notification_task
        
        assert response is None  # Timed out
        
        # Process notification manually
        if not ui._notification_queue.empty():
            msg, urg = ui._notification_queue.get_nowait()
            ui._display_notification(msg, urg)
        
        captured = capsys.readouterr()
        assert "PRODUCTIVITY CHECK" in captured.out
        assert "Urgent: System alert!" in captured.out


# ─────────────────────────────────────────────────────────────────────────────
# Main execution
# ─────────────────────────────────────────────────────────────────────────────


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
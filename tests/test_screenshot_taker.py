"""
Comprehensive regression tests for the screenshot_taker module.

Tests all key functionality including:
- Screenshot capture
- Directory creation with date format
- Filename generation with timestamp
- Multi-monitor validation
- Error handling
- Configuration
- Thread start/stop
"""

import itertools
import logging
import os
import sys
import tempfile
import threading
import time
from datetime import datetime
from pathlib import Path
from unittest.mock import Magic<PERSON>ock, Mock, patch, call

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

import pytest

# Import the modules we're testing
from src.screenshot_taker import ScreenshotRunner
from src.screenshot_taker.config import ScreenshotConfig
from src.screenshot_taker.taker import (
    _todays_folder,
    _timestamp,
    _valid_monitors,
    capture_once,
)


class TestHelperFunctions:
    """Unit tests for helper functions."""

    def test_todays_folder_creates_directory(self):
        """Test that _todays_folder creates the correct directory structure."""
        with tempfile.TemporaryDirectory() as temp_dir:
            root = Path(temp_dir)
            today_str = datetime.now().strftime("%Y%b%d").upper()
            
            folder = _todays_folder(root)
            
            assert folder.exists()
            assert folder.is_dir()
            assert folder.name == today_str
            assert folder.parent == root

    def test_todays_folder_existing_directory(self):
        """Test that _todays_folder handles existing directories gracefully."""
        with tempfile.TemporaryDirectory() as temp_dir:
            root = Path(temp_dir)
            
            # Create the folder first
            folder1 = _todays_folder(root)
            # Call again - should not raise error
            folder2 = _todays_folder(root)
            
            assert folder1 == folder2
            assert folder1.exists()

    def test_timestamp_format(self):
        """Test that _timestamp generates correct format."""
        ts = _timestamp()
        
        # Check format: HHMMSS-microseconds
        assert len(ts) == 13  # 6 digits + dash + 6 digits
        assert ts[6] == '-'
        assert ts[:6].isdigit()
        assert ts[7:].isdigit()
        
        # Verify it's a valid time
        hour = int(ts[:2])
        minute = int(ts[2:4])
        second = int(ts[4:6])
        
        assert 0 <= hour <= 23
        assert 0 <= minute <= 59
        assert 0 <= second <= 59

    def test_timestamp_uniqueness(self):
        """Test that timestamps are unique when called in succession."""
        timestamps = []
        for _ in range(10):
            timestamps.append(_timestamp())
            time.sleep(0.001)  # Small delay to ensure different timestamps
        
        # All timestamps should be unique
        assert len(timestamps) == len(set(timestamps))

    @pytest.mark.parametrize("requested,total,expected", [
        ([1, 2, 3], 5, [1, 2, 3]),
        ([1, 6], 5, [1]),  # 6 is out of range
        ([0, 1, 2], 5, [1, 2]),  # 0 is invalid
        ([-1, 1, 2], 5, [1, 2]),  # negative is invalid
        ([3, 3, 3], 5, [3, 3, 3]),  # duplicates preserved
        ([], 5, []),  # empty request
        ([1], 0, []),  # no monitors available
    ])
    def test_valid_monitors(self, requested, total, expected):
        """Test monitor validation with various inputs."""
        result = _valid_monitors(requested, total)
        assert result == expected


class TestScreenshotConfig:
    """Unit tests for ScreenshotConfig."""

    def test_default_config(self):
        """Test default configuration values."""
        cfg = ScreenshotConfig()
        
        assert cfg.screenshot_interval == 5
        assert cfg.screens_to_capture == [1]
        assert cfg.output_root == Path("./output/screenshots")

    def test_custom_config(self):
        """Test custom configuration values."""
        cfg = ScreenshotConfig(
            screenshot_interval=10,
            screens_to_capture=[1, 2, 3],
            output_root=Path("/custom/path")
        )
        
        assert cfg.screenshot_interval == 10
        assert cfg.screens_to_capture == [1, 2, 3]
        assert cfg.output_root == Path("/custom/path")


class TestCaptureOnce:
    """Unit tests for capture_once function."""

    @patch('src.screenshot_taker.taker.mss.mss')
    def test_capture_once_single_monitor(self, mock_mss_class):
        """Test capturing from a single monitor."""
        # Setup mocks
        mock_sct = MagicMock()
        mock_mss_class.return_value.__enter__.return_value = mock_sct
        
        # Mock monitors (index 0 is "all", then monitor 1)
        mock_sct.monitors = [
            {"top": 0, "left": 0, "width": 2560, "height": 1440},  # all
            {"top": 0, "left": 0, "width": 1920, "height": 1080},  # monitor 1
        ]
        
        # Mock screenshot data
        mock_screenshot = MagicMock()
        mock_screenshot.size = (1920, 1080)
        mock_screenshot.rgb = b'RGB' * (1920 * 1080)  # Mock RGB data
        mock_sct.grab.return_value = mock_screenshot
        
        with tempfile.TemporaryDirectory() as temp_dir:
            cfg = ScreenshotConfig(
                screens_to_capture=[1],
                output_root=Path(temp_dir)
            )
            
            # Mock PIL.Image to avoid actual image processing
            with patch('src.screenshot_taker.taker.Image') as mock_image:
                mock_img_instance = MagicMock()
                mock_image.frombytes.return_value = mock_img_instance
                
                capture_once(cfg)
                
                # Verify mss was called correctly
                mock_sct.grab.assert_called_once_with(mock_sct.monitors[1])
                
                # Verify image was saved
                mock_img_instance.save.assert_called_once()
                save_args = mock_img_instance.save.call_args[0]
                save_path = save_args[0]
                
                # Check the file path
                assert save_path.parent.name == datetime.now().strftime("%Y%b%d").upper()
                assert save_path.suffix == '.png'
                assert save_args[1] == 'PNG'

    @patch('src.screenshot_taker.taker.mss.mss')
    def test_capture_once_multiple_monitors(self, mock_mss_class):
        """Test capturing from multiple monitors."""
        mock_sct = MagicMock()
        mock_mss_class.return_value.__enter__.return_value = mock_sct
        
        # Mock 3 monitors
        mock_sct.monitors = [
            {"top": 0, "left": 0, "width": 3840, "height": 1080},  # all
            {"top": 0, "left": 0, "width": 1920, "height": 1080},  # monitor 1
            {"top": 0, "left": 1920, "width": 1920, "height": 1080},  # monitor 2
        ]
        
        mock_screenshot = MagicMock()
        mock_screenshot.size = (1920, 1080)
        mock_screenshot.rgb = b'RGB' * (1920 * 1080)
        mock_sct.grab.return_value = mock_screenshot
        
        with tempfile.TemporaryDirectory() as temp_dir:
            cfg = ScreenshotConfig(
                screens_to_capture=[1, 2],
                output_root=Path(temp_dir)
            )
            
            with patch('src.screenshot_taker.taker.Image') as mock_image:
                mock_img_instance = MagicMock()
                mock_image.frombytes.return_value = mock_img_instance
                
                capture_once(cfg)
                
                # Should grab both monitors
                assert mock_sct.grab.call_count == 2
                mock_sct.grab.assert_any_call(mock_sct.monitors[1])
                mock_sct.grab.assert_any_call(mock_sct.monitors[2])
                
                # Should save both images
                assert mock_img_instance.save.call_count == 2

    @patch('src.screenshot_taker.taker.mss.mss')
    def test_capture_once_invalid_monitors(self, mock_mss_class):
        """Test behavior with invalid monitor indices."""
        mock_sct = MagicMock()
        mock_mss_class.return_value.__enter__.return_value = mock_sct
        
        # Only 1 monitor available
        mock_sct.monitors = [
            {"top": 0, "left": 0, "width": 1920, "height": 1080},  # all
            {"top": 0, "left": 0, "width": 1920, "height": 1080},  # monitor 1
        ]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            cfg = ScreenshotConfig(
                screens_to_capture=[2, 3, 4],  # All invalid
                output_root=Path(temp_dir)
            )
            
            with patch('src.screenshot_taker.taker._LOG') as mock_log:
                capture_once(cfg)
                
                # Should log warning about no valid screens
                mock_log.warning.assert_called_once()
                assert "No valid screens to capture" in mock_log.warning.call_args[0][0]
                
                # Should not attempt to grab any screenshots
                mock_sct.grab.assert_not_called()

    @patch('src.screenshot_taker.taker.mss.mss')
    def test_capture_once_with_sequence_generator(self, mock_mss_class):
        """Test capture_once with custom sequence generator."""
        mock_sct = MagicMock()
        mock_mss_class.return_value.__enter__.return_value = mock_sct
        
        mock_sct.monitors = [
            {"top": 0, "left": 0, "width": 1920, "height": 1080},  # all
            {"top": 0, "left": 0, "width": 1920, "height": 1080},  # monitor 1
        ]
        
        mock_screenshot = MagicMock()
        mock_screenshot.size = (1920, 1080)
        mock_screenshot.rgb = b'RGB' * (1920 * 1080)
        mock_sct.grab.return_value = mock_screenshot
        
        with tempfile.TemporaryDirectory() as temp_dir:
            cfg = ScreenshotConfig(
                screens_to_capture=[1],
                output_root=Path(temp_dir)
            )
            
            # Custom sequence starting at 100
            seq_gen = itertools.count(100)
            
            with patch('src.screenshot_taker.taker.Image') as mock_image:
                mock_img_instance = MagicMock()
                mock_image.frombytes.return_value = mock_img_instance
                
                capture_once(cfg, seq_gen)
                
                # Check that the filename contains the sequence number
                save_args = mock_img_instance.save.call_args[0]
                save_path = save_args[0]
                filename = save_path.name
                
                # Should contain -0100.png (sequence number 100)
                assert '-0100.png' in filename

    @patch('src.screenshot_taker.taker.mss.mss')
    def test_capture_once_exception_handling(self, mock_mss_class):
        """Test exception handling during capture."""
        mock_sct = MagicMock()
        mock_mss_class.return_value.__enter__.return_value = mock_sct
        
        mock_sct.monitors = [
            {"top": 0, "left": 0, "width": 1920, "height": 1080},  # all
            {"top": 0, "left": 0, "width": 1920, "height": 1080},  # monitor 1
        ]
        
        # Make grab raise an exception
        mock_sct.grab.side_effect = Exception("Screen capture failed")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            cfg = ScreenshotConfig(
                screens_to_capture=[1],
                output_root=Path(temp_dir)
            )
            
            with patch('src.screenshot_taker.taker._LOG') as mock_log:
                # Should not raise exception
                capture_once(cfg)
                
                # Should log the error
                mock_log.warning.assert_called_once()
                assert "Capture failed for monitor" in mock_log.warning.call_args[0][0]


class TestScreenshotRunner:
    """Unit and integration tests for ScreenshotRunner."""

    def test_runner_initialization(self):
        """Test ScreenshotRunner initialization."""
        cfg = ScreenshotConfig(screenshot_interval=10)
        runner = ScreenshotRunner(cfg)
        
        assert runner.cfg == cfg
        assert runner._thread is None
        assert hasattr(runner, '_seq_gen')

    @patch('src.screenshot_taker.capture_once')
    def test_runner_start_stop(self, mock_capture):
        """Test starting and stopping the runner."""
        with tempfile.TemporaryDirectory() as temp_dir:
            cfg = ScreenshotConfig(
                screenshot_interval=0.1,  # Short interval for testing
                output_root=Path(temp_dir)
            )
            runner = ScreenshotRunner(cfg)
            
            # Start the runner
            runner.start()
            assert runner._thread is not None
            assert runner._thread.is_alive()
            
            # Let it run for a bit
            time.sleep(0.3)
            
            # Stop the runner
            runner.stop()
            assert not runner._thread.is_alive()
            
            # Should have captured at least 2 screenshots
            assert mock_capture.call_count >= 2

    @patch('src.screenshot_taker.taker.capture_once')
    def test_runner_already_running(self, mock_capture):
        """Test that starting an already running runner logs warning."""
        with tempfile.TemporaryDirectory() as temp_dir:
            cfg = ScreenshotConfig(
                screenshot_interval=0.1,
                output_root=Path(temp_dir)
            )
            runner = ScreenshotRunner(cfg)
            
            with patch('src.screenshot_taker._LOG') as mock_log:
                # Start the runner
                runner.start()
                
                # Try to start again
                runner.start()
                
                # Should log warning
                mock_log.warning.assert_called_with("Screenshot loop already running")
                
                # Stop the runner
                runner.stop()

    @patch('src.screenshot_taker.capture_once')
    def test_runner_loop_timing(self, mock_capture):
        """Test that the loop respects the configured interval."""
        with tempfile.TemporaryDirectory() as temp_dir:
            cfg = ScreenshotConfig(
                screenshot_interval=0.5,  # 0.5 second interval
                output_root=Path(temp_dir)
            )
            runner = ScreenshotRunner(cfg)
            
            # Make capture_once very fast
            mock_capture.return_value = None
            
            start_time = time.time()
            runner.start()
            
            # Let it run for 1.2 seconds
            time.sleep(1.2)
            
            runner.stop()
            elapsed = time.time() - start_time
            
            # Should have captured 2-3 times (depending on timing)
            # 0s, 0.5s, 1.0s = 3 captures maximum
            assert 2 <= mock_capture.call_count <= 3

    @patch('src.screenshot_taker.taker.capture_once')
    def test_runner_stop_timeout(self, mock_capture):
        """Test runner stop with timeout."""
        with tempfile.TemporaryDirectory() as temp_dir:
            cfg = ScreenshotConfig(
                screenshot_interval=10,  # Long interval
                output_root=Path(temp_dir)
            )
            runner = ScreenshotRunner(cfg)
            
            # Make capture_once block for a long time
            def slow_capture(*args, **kwargs):
                time.sleep(20)
            
            mock_capture.side_effect = slow_capture
            
            runner.start()
            time.sleep(0.1)  # Let thread start
            
            # Stop with short timeout
            start = time.time()
            runner.stop(timeout=0.5)
            elapsed = time.time() - start
            
            # Should return after timeout
            assert elapsed < 1.0

    def test_runner_daemon_thread(self):
        """Test that the runner thread is a daemon thread."""
        cfg = ScreenshotConfig()
        runner = ScreenshotRunner(cfg)
        
        with patch('src.screenshot_taker.taker.capture_once'):
            runner.start()
            
            # Thread should be daemon
            assert runner._thread.daemon
            
            runner.stop()


class TestIntegration:
    """Integration tests that test the full system."""

    def test_full_capture_workflow(self):
        """Test the complete workflow from configuration to file creation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = Path(temp_dir)
            cfg = ScreenshotConfig(
                screenshot_interval=0.1,
                screens_to_capture=[1],
                output_root=output_dir
            )
            
            # Mock mss to avoid actual screenshots
            with patch('src.screenshot_taker.taker.mss.mss') as mock_mss_class:
                mock_sct = MagicMock()
                mock_mss_class.return_value.__enter__.return_value = mock_sct
                
                mock_sct.monitors = [
                    {"top": 0, "left": 0, "width": 1920, "height": 1080},  # all
                    {"top": 0, "left": 0, "width": 1920, "height": 1080},  # monitor 1
                ]
                
                mock_screenshot = MagicMock()
                mock_screenshot.size = (1920, 1080)
                mock_screenshot.rgb = b'RGB' * (1920 * 1080)
                mock_sct.grab.return_value = mock_screenshot
                
                # Mock PIL to create actual files
                with patch('src.screenshot_taker.taker.Image') as mock_image:
                    mock_img_instance = MagicMock()
                    mock_image.frombytes.return_value = mock_img_instance
                    
                    # Make save actually create a file
                    def mock_save(path, format):
                        path.touch()
                    
                    mock_img_instance.save.side_effect = mock_save
                    
                    # Run the capture
                    runner = ScreenshotRunner(cfg)
                    runner.start()
                    time.sleep(0.3)  # Let it capture a few times
                    runner.stop()
                    
                    # Check that files were created
                    today_folder = output_dir / datetime.now().strftime("%Y%b%d").upper()
                    assert today_folder.exists()
                    
                    png_files = list(today_folder.glob("*.png"))
                    assert len(png_files) >= 2
                    
                    # Check filename format
                    for png_file in png_files:
                        filename = png_file.name
                        # Format: HHMMSS-microseconds-sequence.png
                        parts = filename.split('-')
                        assert len(parts) == 3  # HHMMSS, microseconds, sequence.png
                        assert parts[0].isdigit() and len(parts[0]) == 6  # HHMMSS
                        assert parts[1].isdigit() and len(parts[1]) == 6  # microseconds
                        assert parts[2].endswith('.png')
                        seq_part = parts[2][:-4]  # Remove .png
                        assert seq_part.isdigit() and len(seq_part) == 4  # 4-digit sequence


class TestErrorScenarios:
    """Test various error scenarios."""

    @patch('src.screenshot_taker.taker.mss.mss')
    def test_no_monitors_available(self, mock_mss_class):
        """Test behavior when no monitors are available."""
        mock_sct = MagicMock()
        mock_mss_class.return_value.__enter__.return_value = mock_sct
        
        # Only the "all" monitor (no physical monitors)
        mock_sct.monitors = [
            {"top": 0, "left": 0, "width": 0, "height": 0},  # all
        ]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            cfg = ScreenshotConfig(
                screens_to_capture=[1],
                output_root=Path(temp_dir)
            )
            
            with patch('src.screenshot_taker.taker._LOG') as mock_log:
                capture_once(cfg)
                
                # Should log warning
                mock_log.warning.assert_called_once()

    def test_output_directory_permission_error(self):
        """Test handling of permission errors when creating output directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a read-only directory
            readonly_dir = Path(temp_dir) / "readonly"
            readonly_dir.mkdir()
            os.chmod(readonly_dir, 0o444)
            
            try:
                cfg = ScreenshotConfig(
                    output_root=readonly_dir / "screenshots"
                )
                
                with patch('src.screenshot_taker.taker._LOG') as mock_log:
                    # This should fail when trying to create the date folder
                    with pytest.raises(PermissionError):
                        _todays_folder(cfg.output_root)
            finally:
                # Restore permissions for cleanup
                os.chmod(readonly_dir, 0o755)


def main():
    """Run all tests and show example output."""
    print("Running screenshot_taker regression tests...")
    print("=" * 60)
    
    # Configure logging for the test run
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )
    
    # Run pytest with verbose output
    pytest_args = [
        __file__,
        "-v",  # Verbose
        "-s",  # Don't capture stdout
        "--tb=short",  # Short traceback format
    ]
    
    exit_code = pytest.main(pytest_args)
    
    print("\n" + "=" * 60)
    print("Test Summary:")
    print("=" * 60)
    
    if exit_code == 0:
        print("✓ All tests passed!")
    else:
        print("✗ Some tests failed.")
    
    # Show example usage
    print("\nExample Usage:")
    print("-" * 60)
    print("""
from src.screenshot_taker import ScreenshotRunner
from src.screenshot_taker.config import ScreenshotConfig
from pathlib import Path

# Configure screenshot capture
config = ScreenshotConfig(
    screenshot_interval=5,  # Capture every 5 seconds
    screens_to_capture=[1],  # Capture primary monitor
    output_root=Path("./screenshots")
)

# Create and start the runner
runner = ScreenshotRunner(config)
runner.start()

# Let it run...
# Screenshots will be saved to ./screenshots/YYYYMMMDD/HHMMSS-microseconds-NNNN.png

# Stop when done
runner.stop()
""")
    
    return exit_code


if __name__ == "__main__":
    exit(main())
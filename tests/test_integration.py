"""
Comprehensive integration tests for the Feisty4 system.

Tests cover:
- Full system workflows
- Module interactions
- End-to-end scenarios
- Performance under load
- Error propagation
- Data consistency
"""

import asyncio
import json
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import List
from unittest.mock import Mock, patch, AsyncMock, MagicMock

import pytest

from src.interfaces import (
    AIAnalysisResult,
    EscalationLevel,
    EscalationState,
    JustificationRequest,
    JustificationResponse,
    ScreenshotData,
    TaskData,
    TaskPriority,
    TaskStatus,
)
from src.task_manager.manager import TaskManager
from src.local_store.store import LocalStore
from src.user_interface.interface import UserInterface
from src.reasoning_ai.reasoner import ReasoningAI
from src.escalator.escalator import Escalator
from tests.test_utils import (
    create_test_environment,
    generate_tasks,
    wait_for_condition,
    PerformanceTimer,
)


class TestSystemIntegration:
    """Integration tests for complete system workflows."""
    
    # ─────────────────────────────────────────────────────────────────────────
    # Basic Workflow Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    @pytest.mark.asyncio
    async def test_basic_monitoring_workflow(self, temp_dir):
        """Test basic monitoring workflow with all components."""
        # Initialize components
        test_env = create_test_environment(temp_dir)
        
        task_manager = TaskManager(storage_path=test_env["tasks"] / "tasks.json")
        local_store = LocalStore(base_dir=test_env["base"])
        ui = UserInterface(timeout=5)
        reasoner = ReasoningAI()  # Mock mode
        escalator = Escalator()
        
        # Create tasks
        tasks = [
            task_manager.add_task("Implement authentication", TaskPriority.P1),
            task_manager.add_task("Write tests", TaskPriority.P2),
            task_manager.add_task("Update documentation", TaskPriority.P3),
        ]
        
        # Get current task
        current_task = task_manager.get_current_task()
        assert current_task.description == "Implement authentication"
        
        # Simulate screenshot
        screenshot_path = test_env["screenshots"] / "test_screenshot.png"
        screenshot_path.write_bytes(b"MOCK_PNG_DATA")
        
        screenshot_data: ScreenshotData = {
            "path": str(screenshot_path),
            "timestamp": datetime.now().isoformat(),
            "screen_id": 1
        }
        
        # Save screenshot metadata
        local_store.save_screenshot_metadata(screenshot_data)
        
        # Analyze screenshot
        analysis = await reasoner.analyze_screenshot(screenshot_data, current_task)
        assert isinstance(analysis, AIAnalysisResult)
        
        # Save analysis
        local_store.save_analysis_log(analysis)
        
        # Process through escalator
        state = escalator.process_analysis(analysis)
        assert isinstance(state, EscalationState)
        
        # If off-task, would trigger justification request
        if not analysis.on_task:
            request = JustificationRequest(
                timestamp=datetime.now(),
                detected_activity="Unknown activity",
                expected_task=current_task.description
            )
            
            # In real system, would prompt user
            # Here we'll simulate no response (timeout)
            response = None
            
            if response:
                local_store.save_analysis_log(analysis, response)
                local_store.save_justification_log(response)
    
    @pytest.mark.asyncio
    async def test_task_completion_workflow(self, temp_dir):
        """Test workflow when user completes tasks."""
        # Setup
        test_env = create_test_environment(temp_dir)
        task_manager = TaskManager(storage_path=test_env["tasks"] / "tasks.json")
        local_store = LocalStore(base_dir=test_env["base"])
        
        # Create and start task
        task = task_manager.add_task("Fix login bug", TaskPriority.P1)
        task_manager.update_task_status(task.id, TaskStatus.IN_PROGRESS)
        
        # Simulate working on task (multiple on-task analyses)
        for i in range(5):
            screenshot_data: ScreenshotData = {
                "path": f"/tmp/screenshot_{i}.png",
                "timestamp": (datetime.now() + timedelta(minutes=i*10)).isoformat(),
                "screen_id": 1
            }
            
            analysis = AIAnalysisResult(
                on_task=True,
                confidence=0.9 + i*0.01,
                reason=f"User coding in IDE - iteration {i}",
                screenshot_ref=screenshot_data["path"]
            )
            
            local_store.save_analysis_log(analysis)
        
        # Complete task
        completed_task = task_manager.mark_complete(task.id)
        assert completed_task.status == TaskStatus.COMPLETED
        assert completed_task.completed_at is not None
        
        # Save updated task list
        local_store.save_task_list(task_manager.get_all_tasks())
        
        # Get activity summary
        summary = local_store.get_activity_summary(
            datetime.now() - timedelta(hours=1),
            datetime.now()
        )
        
        assert summary["total_analyses"] == 5
        assert summary["on_task_count"] == 5
        assert summary["off_task_count"] == 0
    
    @pytest.mark.asyncio
    async def test_escalation_workflow(self, temp_dir):
        """Test complete escalation workflow."""
        # Setup components
        test_env = create_test_environment(temp_dir)
        
        task_manager = TaskManager(storage_path=test_env["tasks"] / "tasks.json")
        local_store = LocalStore(base_dir=test_env["base"])
        escalator = Escalator()
        escalator.config.prompts_before_escalation = 2  # Faster escalation for test
        
        # Create task
        task = task_manager.add_task("Complete code review", TaskPriority.P1)
        
        # Simulate repeated off-task behavior
        off_task_analyses = []
        for i in range(6):  # Enough to trigger multiple escalations
            analysis = AIAnalysisResult(
                on_task=False,
                confidence=0.85,
                reason=f"User browsing social media - incident {i+1}",
                screenshot_ref=f"/tmp/screenshot_{i}.png"
            )
            
            # Process through escalator
            state = escalator.process_analysis(analysis)
            
            # Save analysis
            local_store.save_analysis_log(analysis)
            
            # Record prompt
            request = JustificationRequest(
                timestamp=datetime.now(),
                detected_activity="Browsing social media",
                expected_task=task.description
            )
            escalator.record_prompt(request)
            
            off_task_analyses.append((analysis, state))
        
        # Verify escalation progression
        states = [state for _, state in off_task_analyses]
        
        # Should have escalated through levels
        assert states[0].level == EscalationLevel.NORMAL
        assert states[2].level == EscalationLevel.WARNING  # After 2 prompts
        assert states[4].level == EscalationLevel.FREQUENT  # After 2 more
        
        # Check intervention recommendations
        assert escalator.get_intervention_action().value != "none"
    
    # ─────────────────────────────────────────────────────────────────────────
    # Data Consistency Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_task_persistence_consistency(self, temp_dir):
        """Test task data consistency across save/load cycles."""
        test_env = create_test_environment(temp_dir)
        
        # Create and save tasks
        manager1 = TaskManager(storage_path=test_env["tasks"] / "tasks.json")
        store1 = LocalStore(base_dir=test_env["base"])
        
        tasks = []
        for i in range(10):
            task = manager1.add_task(f"Task {i+1}", TaskPriority((i % 5) + 1))
            if i % 3 == 0:
                manager1.update_task_status(task.id, TaskStatus.IN_PROGRESS)
            if i % 4 == 0:
                manager1.mark_complete(task.id)
            tasks.append(task)
        
        # Save via both manager and store
        store1.save_task_list(manager1.get_all_tasks())
        
        # Load in new instances
        manager2 = TaskManager(storage_path=test_env["tasks"] / "tasks.json")
        store2 = LocalStore(base_dir=test_env["base"])
        
        loaded_tasks_manager = manager2.get_all_tasks()
        loaded_tasks_store = store2.load_task_list()
        
        # Verify consistency
        assert len(loaded_tasks_manager) == len(tasks)
        assert len(loaded_tasks_store) == len(tasks)
        
        # Verify task details preserved
        for original, loaded_m, loaded_s in zip(tasks, loaded_tasks_manager, loaded_tasks_store):
            assert loaded_m.id == original.id
            assert loaded_m.status == original.status
            assert loaded_s.id == original.id
            assert loaded_s.status == original.status
    
    def test_analysis_log_consistency(self, temp_dir):
        """Test analysis log consistency with justifications."""
        test_env = create_test_environment(temp_dir)
        store = LocalStore(base_dir=test_env["base"])
        
        # Create interleaved analyses and justifications
        for i in range(5):
            analysis = AIAnalysisResult(
                on_task=(i % 2 == 0),
                confidence=0.8 + i*0.02,
                reason=f"Activity {i}",
                screenshot_ref=f"/tmp/screenshot_{i}.png"
            )
            
            if not analysis.on_task:
                # Add justification for off-task
                justification = JustificationResponse(
                    user_input=f"Justification for activity {i}",
                    valid=(i % 3 != 0),
                    timestamp=datetime.now()
                )
                store.save_analysis_log(analysis, justification)
                store.save_justification_log(justification)
            else:
                store.save_analysis_log(analysis)
        
        # Get summary and verify
        summary = store.get_activity_summary(
            datetime.now() - timedelta(hours=1),
            datetime.now()
        )
        
        assert summary["total_analyses"] == 5
        assert summary["on_task_count"] == 3  # i=0,2,4
        assert summary["off_task_count"] == 2  # i=1,3
        assert summary["justifications_provided"] == 2
        assert summary["justifications_accepted"] == 1  # i=1 (i=3 invalid)
    
    # ─────────────────────────────────────────────────────────────────────────
    # Error Propagation Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    @pytest.mark.asyncio
    async def test_error_propagation_screenshot_missing(self, temp_dir):
        """Test system handles missing screenshot gracefully."""
        test_env = create_test_environment(temp_dir)
        
        task_manager = TaskManager(storage_path=test_env["tasks"] / "tasks.json")
        local_store = LocalStore(base_dir=test_env["base"])
        reasoner = ReasoningAI()
        
        # Create task
        task = task_manager.add_task("Debug issue", TaskPriority.P1)
        
        # Reference non-existent screenshot
        screenshot_data: ScreenshotData = {
            "path": "/nonexistent/screenshot.png",
            "timestamp": datetime.now().isoformat(),
            "screen_id": 1
        }
        
        # Should handle missing file
        local_store.save_screenshot_metadata(screenshot_data)  # Should work
        
        # Analysis should still work (mock mode doesn't read file)
        analysis = await reasoner.analyze_screenshot(screenshot_data, task)
        assert isinstance(analysis, AIAnalysisResult)
    
    @pytest.mark.asyncio
    async def test_error_propagation_api_failure(self, temp_dir):
        """Test system handles API failures gracefully."""
        with patch('openai.OpenAI') as mock_openai:
            # Setup API to fail
            mock_client = Mock()
            mock_openai.return_value = mock_client
            mock_client.chat.completions.create.side_effect = Exception("API Error")
            
            # Create real screenshot
            test_env = create_test_environment(temp_dir)
            screenshot_path = test_env["screenshots"] / "test.png"
            screenshot_path.write_bytes(b"PNG_DATA")
            
            screenshot_data: ScreenshotData = {
                "path": str(screenshot_path),
                "timestamp": datetime.now().isoformat(),
                "screen_id": 1
            }
            
            # Initialize in API mode
            from src.reasoning_ai.config import ReasoningConfig
            config = ReasoningConfig(mock_mode=False, openai_api_key="test-key")
            reasoner = ReasoningAI(config=config)
            
            task = TaskData(
                id="test-001",
                priority=TaskPriority.P1,
                description="Test task",
                status=TaskStatus.IN_PROGRESS,
                created_at=datetime.now()
            )
            
            # Should fallback to mock mode
            analysis = await reasoner.analyze_screenshot(screenshot_data, task)
            assert isinstance(analysis, AIAnalysisResult)
    
    # ─────────────────────────────────────────────────────────────────────────
    # Performance Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    @pytest.mark.asyncio
    async def test_performance_concurrent_analyses(self, temp_dir):
        """Test system performance with concurrent analyses."""
        test_env = create_test_environment(temp_dir)
        
        task_manager = TaskManager(storage_path=test_env["tasks"] / "tasks.json")
        local_store = LocalStore(base_dir=test_env["base"])
        reasoner = ReasoningAI()
        
        # Create tasks
        tasks = generate_tasks(5)
        for task in tasks:
            task_manager.add_task(task.description, task.priority)
        
        current_task = task_manager.get_current_task()
        
        # Create multiple screenshots
        screenshots = []
        for i in range(10):
            screenshot_data: ScreenshotData = {
                "path": f"/tmp/screenshot_{i}.png",
                "timestamp": datetime.now().isoformat(),
                "screen_id": (i % 2) + 1
            }
            screenshots.append(screenshot_data)
        
        # Analyze concurrently
        with PerformanceTimer("Concurrent analyses") as timer:
            analyses = await asyncio.gather(*[
                reasoner.analyze_screenshot(screenshot, current_task)
                for screenshot in screenshots
            ])
        
        # Should complete quickly (mock delay is 0.5s, concurrent should be ~0.5s total)
        timer.assert_faster_than(1.0)
        
        # Save all analyses
        with PerformanceTimer("Saving analyses") as timer:
            for analysis in analyses:
                local_store.save_analysis_log(analysis)
        
        timer.assert_faster_than(0.5)
        
        assert len(analyses) == 10
    
    def test_performance_large_task_list(self, temp_dir):
        """Test system performance with large task list."""
        test_env = create_test_environment(temp_dir)
        
        # Create many tasks
        with PerformanceTimer("Creating 1000 tasks") as timer:
            manager = TaskManager(storage_path=test_env["tasks"] / "tasks.json")
            
            for i in range(1000):
                manager.add_task(
                    f"Task {i+1}: {'x' * 50}",  # Reasonably long descriptions
                    TaskPriority((i % 5) + 1)
                )
        
        timer.assert_faster_than(5.0)
        
        # Get current task (should be fast even with 1000 tasks)
        with PerformanceTimer("Getting current task") as timer:
            current = manager.get_current_task()
        
        timer.assert_faster_than(0.01)
        assert current is not None
        
        # Get all tasks sorted
        with PerformanceTimer("Getting all tasks sorted") as timer:
            all_tasks = manager.get_all_tasks()
        
        timer.assert_faster_than(0.1)
        assert len(all_tasks) == 1000
    
    # ─────────────────────────────────────────────────────────────────────────
    # Complex Scenario Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    @pytest.mark.asyncio
    async def test_workday_simulation(self, temp_dir):
        """Simulate a workday with mixed behavior patterns."""
        test_env = create_test_environment(temp_dir)
        
        # Initialize all components
        task_manager = TaskManager(storage_path=test_env["tasks"] / "tasks.json")
        local_store = LocalStore(base_dir=test_env["base"])
        reasoner = ReasoningAI()
        escalator = Escalator()
        
        # Import daily tasks
        daily_tasks = [
            "Morning standup preparation",
            "Fix critical production bug",
            "Code review for team",
            "Implement new feature",
            "Update documentation",
            "Afternoon meeting prep"
        ]
        task_manager.import_tasks(daily_tasks)
        
        # Simulate 8-hour workday (every 30 min = 16 checks)
        workday_analyses = []
        
        # Morning - mostly on task
        for i in range(6):  # 3 hours
            analysis = AIAnalysisResult(
                on_task=True,
                confidence=0.85 + (i * 0.02),
                reason="User actively working",
                screenshot_ref=f"/tmp/morning_{i}.png"
            )
            workday_analyses.append(analysis)
            escalator.process_analysis(analysis)
        
        # Mid-morning distraction
        for i in range(2):  # 1 hour
            analysis = AIAnalysisResult(
                on_task=False,
                confidence=0.8,
                reason="Browsing news",
                screenshot_ref=f"/tmp/distraction_{i}.png"
            )
            workday_analyses.append(analysis)
            state = escalator.process_analysis(analysis)
        
        # Back to work
        for i in range(4):  # 2 hours
            analysis = AIAnalysisResult(
                on_task=True,
                confidence=0.9,
                reason="Coding intensively",
                screenshot_ref=f"/tmp/afternoon_{i}.png"
            )
            workday_analyses.append(analysis)
            escalator.process_analysis(analysis)
        
        # Complete some tasks
        all_tasks = task_manager.get_all_tasks()
        task_manager.mark_complete(all_tasks[0].id)  # Standup prep
        task_manager.mark_complete(all_tasks[1].id)  # Bug fix
        
        # End of day distraction
        for i in range(4):  # 2 hours
            on_task = i < 2  # First hour on task, then off
            analysis = AIAnalysisResult(
                on_task=on_task,
                confidence=0.75,
                reason="End of day activity",
                screenshot_ref=f"/tmp/evening_{i}.png"
            )
            workday_analyses.append(analysis)
            escalator.process_analysis(analysis)
        
        # Save all analyses
        for analysis in workday_analyses:
            local_store.save_analysis_log(analysis)
        
        # Generate end-of-day summary
        summary = local_store.get_activity_summary(
            datetime.now() - timedelta(hours=8),
            datetime.now()
        )
        
        assert summary["total_analyses"] == 16
        assert summary["on_task_count"] == 12  # 75% on-task
        assert summary["off_task_count"] == 4
        
        # Check final escalation state
        final_state = escalator.get_current_state()
        stats = escalator.get_statistics()
        
        # Should have some escalation history
        assert stats["off_task_incidents"] > 0
        assert stats["total_prompts"] >= stats["off_task_incidents"]
    
    @pytest.mark.asyncio
    async def test_recovery_after_restart(self, temp_dir):
        """Test system recovers state correctly after restart."""
        test_env = create_test_environment(temp_dir)
        
        # Phase 1: Initial session
        manager1 = TaskManager(storage_path=test_env["tasks"] / "tasks.json")
        store1 = LocalStore(base_dir=test_env["base"])
        escalator1 = Escalator()
        
        # Create tasks and work on them
        task1 = manager1.add_task("Morning task", TaskPriority.P1)
        task2 = manager1.add_task("Afternoon task", TaskPriority.P2)
        
        manager1.update_task_status(task1.id, TaskStatus.IN_PROGRESS)
        
        # Generate some history
        for i in range(5):
            analysis = AIAnalysisResult(
                on_task=(i % 2 == 0),
                confidence=0.85,
                reason=f"Activity {i}",
                screenshot_ref=f"/tmp/screenshot_{i}.png"
            )
            store1.save_analysis_log(analysis)
            escalator1.process_analysis(analysis)
        
        # Save state
        store1.save_task_list(manager1.get_all_tasks())
        initial_stats = escalator1.get_statistics()
        
        # Phase 2: Restart with new instances
        manager2 = TaskManager(storage_path=test_env["tasks"] / "tasks.json")
        store2 = LocalStore(base_dir=test_env["base"])
        escalator2 = Escalator()  # Note: escalator state not persisted
        
        # Verify task state recovered
        tasks = manager2.get_all_tasks()
        assert len(tasks) == 2
        
        current = manager2.get_current_task()
        assert current.id == task1.id
        assert current.status == TaskStatus.IN_PROGRESS
        
        # Verify can read historical data
        summary = store2.get_activity_summary(
            datetime.now() - timedelta(hours=1),
            datetime.now()
        )
        assert summary["total_analyses"] == 5
        
        # Continue working
        manager2.mark_complete(task1.id)
        new_current = manager2.get_current_task()
        assert new_current.id == task2.id


# ─────────────────────────────────────────────────────────────────────────────
# Module Interaction Tests
# ─────────────────────────────────────────────────────────────────────────────


class TestModuleInteractions:
    """Test specific interactions between modules."""
    
    def test_task_manager_local_store_sync(self, temp_dir):
        """Test TaskManager and LocalStore stay in sync."""
        test_env = create_test_environment(temp_dir)
        
        manager = TaskManager(storage_path=test_env["tasks"] / "tasks.json")
        store = LocalStore(base_dir=test_env["base"])
        
        # Both should handle the same file
        tasks_from_manager = []
        for i in range(5):
            task = manager.add_task(f"Task {i}", TaskPriority.P1)
            tasks_from_manager.append(task)
        
        # Save through manager (auto-save)
        manager._save_tasks()
        
        # Load through store
        tasks_from_store = store.load_task_list()
        
        assert len(tasks_from_store) == len(tasks_from_manager)
        for tm, ts in zip(tasks_from_manager, tasks_from_store):
            assert tm.id == ts.id
            assert tm.description == ts.description
    
    @pytest.mark.asyncio
    async def test_reasoner_escalator_interaction(self):
        """Test ReasoningAI and Escalator work together correctly."""
        reasoner = ReasoningAI()
        escalator = Escalator()
        
        task = TaskData(
            id="test-001",
            priority=TaskPriority.P1,
            description="Important task",
            status=TaskStatus.IN_PROGRESS,
            created_at=datetime.now()
        )
        
        screenshot: ScreenshotData = {
            "path": "/tmp/test.png",
            "timestamp": datetime.now().isoformat(),
            "screen_id": 1
        }
        
        # Generate mixed analyses
        for i in range(10):
            analysis = await reasoner.analyze_screenshot(screenshot, task)
            state = escalator.process_analysis(analysis)
            
            # Escalator should respond to analysis results
            if not analysis.on_task:
                assert escalator._off_task_history[-1][1] == analysis
    
    def test_ui_escalator_intervention_flow(self):
        """Test UI receives appropriate notifications from Escalator."""
        ui = UserInterface()
        escalator = Escalator()
        
        # Escalate to warning level
        escalator._current_state = EscalationState(
            level=EscalationLevel.WARNING,
            started_at=datetime.now(),
            prompt_count=0
        )
        
        # Get intervention action
        action = escalator.get_intervention_action()
        
        # UI should be able to handle the notification
        if action.value == "notify":
            ui.send_notification("Warning: Please return to your task", urgency="high")
            
            # Verify notification queued
            assert not ui._notification_queue.empty()
            msg, urgency = ui._notification_queue.get_nowait()
            assert "Warning" in msg
            assert urgency == "high"


# ─────────────────────────────────────────────────────────────────────────────
# Main execution
# ─────────────────────────────────────────────────────────────────────────────


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
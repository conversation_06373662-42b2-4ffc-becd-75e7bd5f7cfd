# Feisty4 Test Suite

Comprehensive test suite for the Feisty4 Productivity Guard system.

## Overview

The test suite includes:
- **Unit Tests**: Testing individual modules in isolation
- **Integration Tests**: Testing module interactions and full workflows
- **Performance Tests**: Ensuring system meets performance requirements
- **Error Handling Tests**: Verifying graceful error handling

## Test Coverage Goals

- Target: >80% code coverage across all modules
- Critical paths: 100% coverage
- Error handling: Comprehensive testing of edge cases

## Test Structure

```
tests/
├── conftest.py              # Shared fixtures and configuration
├── test_utils.py            # Test helper functions
├── test_task_manager.py     # Task Manager module tests
├── test_local_store.py      # Local Store module tests  
├── test_user_interface.py   # User Interface module tests
├── test_reasoning_ai.py     # Reasoning AI module tests
├── test_escalator.py        # Escalator module tests
└── test_integration.py      # Full system integration tests
```

## Running Tests

### Run All Tests
```bash
# Basic run
pytest

# With coverage
pytest --cov=src --cov-report=html

# Verbose output
pytest -v

# Using the test runner script
python run_tests.py
```

### Run Specific Module Tests
```bash
# Test single module
pytest tests/test_task_manager.py

# Using test runner
python run_tests.py --module task_manager
```

### Run by Test Type
```bash
# Unit tests only
python run_tests.py --unit

# Integration tests only
python run_tests.py --integration
```

### Run with Markers
```bash
# Skip slow tests
pytest -m "not slow"

# Run only async tests
pytest -m "asyncio"
```

### Parallel Execution
```bash
# Run tests in parallel (requires pytest-xdist)
pip install pytest-xdist
pytest -n auto

# Or using test runner
python run_tests.py --parallel
```

## Test Categories

### Unit Tests

Each module has comprehensive unit tests covering:
- Initialization and configuration
- Core functionality
- Edge cases
- Error handling
- Performance requirements

### Integration Tests

Full system workflows including:
- Basic monitoring workflow
- Task completion workflow
- Escalation scenarios
- Data persistence and recovery
- Multi-module interactions

### Mock vs Real Mode

- Most tests use mock mode for external dependencies
- OpenAI API calls are mocked to avoid costs
- File system operations use temporary directories

## Writing New Tests

### Test Structure
```python
class TestModuleName:
    """Test suite for ModuleName."""
    
    def test_feature_basic(self):
        """Test basic feature functionality."""
        # Setup
        # Action
        # Assert
    
    @pytest.mark.asyncio
    async def test_async_feature(self):
        """Test async functionality."""
        # Async test implementation
```

### Using Fixtures
```python
def test_with_fixtures(temp_dir, sample_tasks):
    """Test using provided fixtures."""
    # temp_dir provides temporary directory
    # sample_tasks provides test task data
```

### Performance Testing
```python
def test_performance(self):
    """Test performance requirements."""
    from tests.test_utils import PerformanceTimer
    
    with PerformanceTimer("Operation") as timer:
        # Perform operation
        pass
    
    timer.assert_faster_than(1.0)  # Must complete in < 1 second
```

## Continuous Integration

The test suite is designed to run in CI/CD pipelines:

```yaml
# Example GitHub Actions configuration
- name: Run tests
  run: |
    pip install -e .
    pip install pytest pytest-cov pytest-asyncio
    python run_tests.py --coverage
```

## Coverage Reports

After running tests with coverage:
1. HTML report: `htmlcov/index.html`
2. Terminal report: Shows missing lines
3. Coverage badge: Can be generated for README

## Common Issues

### Import Errors
- Ensure you've installed the package: `pip install -e .`
- Check PYTHONPATH includes the project root

### Async Test Failures
- Ensure pytest-asyncio is installed
- Use `@pytest.mark.asyncio` decorator

### Temporary File Issues
- Tests use pytest's `tmp_path` fixture
- Cleanup is automatic

### Mock Failures
- Check mock setup matches actual implementation
- Verify mock return values are correct types

## Test Data

Test fixtures provide:
- Sample tasks with various states
- Mock screenshot data
- Sample analysis results
- Test configuration objects

See `conftest.py` for available fixtures.

## Contributing

When adding new features:
1. Write tests first (TDD approach)
2. Ensure tests cover happy path and edge cases
3. Add integration tests for cross-module features
4. Run full test suite before submitting
5. Maintain >80% coverage for new code
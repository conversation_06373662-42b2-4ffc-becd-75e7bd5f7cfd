"""
Test utilities and helper functions for the Feisty4 test suite.
"""

import asyncio
import json
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Any, Dict, List, Optional
from unittest.mock import Mock, patch

from src.interfaces import (
    AIAnalysisResult,
    TaskData,
    TaskPriority,
    TaskStatus,
)


# ─────────────────────────────────────────────────────────────────────────────
# Async Helpers
# ─────────────────────────────────────────────────────────────────────────────


def async_return(value: Any):
    """Create an async function that returns the given value."""
    async def _async_return():
        return value
    return _async_return()


def async_raise(exception: Exception):
    """Create an async function that raises the given exception."""
    async def _async_raise():
        raise exception
    return _async_raise()


class AsyncContextManager:
    """Helper for creating async context managers in tests."""
    
    def __init__(self, return_value=None):
        self.return_value = return_value
    
    async def __aenter__(self):
        return self.return_value
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        return False


# ─────────────────────────────────────────────────────────────────────────────
# Data Generation
# ─────────────────────────────────────────────────────────────────────────────


def generate_tasks(count: int, base_time: Optional[datetime] = None) -> List[TaskData]:
    """Generate a list of test tasks with varied attributes."""
    if base_time is None:
        base_time = datetime.now()
    
    tasks = []
    statuses = list(TaskStatus)
    
    for i in range(count):
        # Cycle through priorities and statuses
        priority = TaskPriority((i % 5) + 1)
        status = statuses[i % len(statuses)]
        
        # Create varied creation times
        created_at = base_time - timedelta(hours=i)
        
        # Add completed_at for completed tasks
        completed_at = None
        if status == TaskStatus.COMPLETED:
            completed_at = created_at + timedelta(minutes=30 + i * 10)
        
        task = TaskData(
            id=f"task-{i+1:03d}",
            priority=priority,
            description=f"Test task {i+1}: {status.value} with priority P{priority.value}",
            status=status,
            created_at=created_at,
            completed_at=completed_at
        )
        tasks.append(task)
    
    return tasks


def generate_analysis_results(
    count: int,
    on_task_ratio: float = 0.7,
    base_confidence: float = 0.8
) -> List[AIAnalysisResult]:
    """Generate a list of test analysis results."""
    results = []
    
    for i in range(count):
        on_task = i < int(count * on_task_ratio)
        confidence = base_confidence + (i % 20) * 0.01  # Vary confidence slightly
        
        if on_task:
            reason = f"User working on task {i+1}"
        else:
            reason = f"User browsing social media instead of task {i+1}"
        
        result = AIAnalysisResult(
            on_task=on_task,
            confidence=min(confidence, 1.0),
            reason=reason,
            screenshot_ref=f"/tmp/screenshot_{i+1:03d}.png"
        )
        results.append(result)
    
    return results


# ─────────────────────────────────────────────────────────────────────────────
# File Helpers
# ─────────────────────────────────────────────────────────────────────────────


def create_date_folders(base_path: Path, days_back: int = 7) -> List[Path]:
    """Create date-based folders for testing cleanup operations."""
    folders = []
    today = datetime.now()
    
    for i in range(days_back):
        date = today - timedelta(days=i)
        folder_name = date.strftime("%Y%b%d").upper()
        folder_path = base_path / folder_name
        folder_path.mkdir(parents=True, exist_ok=True)
        folders.append(folder_path)
        
        # Add some dummy files
        (folder_path / "test.png").write_bytes(b"PNG")
        (folder_path / "analysis.jsonl").write_text("{}")
    
    return folders


def create_mock_screenshot_files(
    directory: Path,
    count: int = 5,
    prefix: str = "screenshot"
) -> List[Path]:
    """Create mock screenshot files for testing."""
    files = []
    
    for i in range(count):
        timestamp = datetime.now() + timedelta(seconds=i * 30)
        filename = f"{timestamp.strftime('%H%M%S')}-{i+1:06d}-{prefix}.png"
        filepath = directory / filename
        filepath.write_bytes(b"MOCK_PNG_DATA")
        files.append(filepath)
    
    return files


# ─────────────────────────────────────────────────────────────────────────────
# JSON/JSONL Helpers
# ─────────────────────────────────────────────────────────────────────────────


def read_jsonl(file_path: Path) -> List[Dict[str, Any]]:
    """Read all entries from a JSONL file."""
    entries = []
    with open(file_path, 'r') as f:
        for line in f:
            if line.strip():
                entries.append(json.loads(line))
    return entries


def write_jsonl(file_path: Path, entries: List[Dict[str, Any]]) -> None:
    """Write entries to a JSONL file."""
    with open(file_path, 'w') as f:
        for entry in entries:
            json.dump(entry, f)
            f.write('\n')


# ─────────────────────────────────────────────────────────────────────────────
# Mock Helpers
# ─────────────────────────────────────────────────────────────────────────────


class MockAsyncIterator:
    """Helper for mocking async iterators in tests."""
    
    def __init__(self, items):
        self.items = items
        self.index = 0
    
    def __aiter__(self):
        return self
    
    async def __anext__(self):
        if self.index >= len(self.items):
            raise StopAsyncIteration
        value = self.items[self.index]
        self.index += 1
        return value


def create_mock_config(**kwargs) -> Mock:
    """Create a mock configuration object with sensible defaults."""
    defaults = {
        "storage_path": Path("/tmp/test"),
        "screenshot_interval": 30,
        "max_storage_days": 7,
        "compression_enabled": True,
        "confidence_threshold": 0.7,
        "auto_save": True,
    }
    defaults.update(kwargs)
    
    mock_config = Mock()
    for key, value in defaults.items():
        setattr(mock_config, key, value)
    
    return mock_config


# ─────────────────────────────────────────────────────────────────────────────
# Assertion Helpers
# ─────────────────────────────────────────────────────────────────────────────


def assert_task_equal(task1: TaskData, task2: TaskData, ignore_timestamps: bool = False):
    """Assert two tasks are equal, optionally ignoring timestamps."""
    assert task1.id == task2.id
    assert task1.priority == task2.priority
    assert task1.description == task2.description
    assert task1.status == task2.status
    
    if not ignore_timestamps:
        assert task1.created_at == task2.created_at
        assert task1.completed_at == task2.completed_at


def assert_analysis_result_valid(result: AIAnalysisResult):
    """Assert an analysis result has valid values."""
    assert isinstance(result.on_task, bool)
    assert 0.0 <= result.confidence <= 1.0
    assert isinstance(result.reason, str) and len(result.reason) > 0
    assert isinstance(result.screenshot_ref, str) and len(result.screenshot_ref) > 0


def assert_file_contains_json(file_path: Path, expected_keys: List[str]):
    """Assert a file contains valid JSON with expected keys."""
    assert file_path.exists()
    
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    for key in expected_keys:
        assert key in data, f"Expected key '{key}' not found in JSON"


# ─────────────────────────────────────────────────────────────────────────────
# Time Helpers
# ─────────────────────────────────────────────────────────────────────────────


class TimeAdvancer:
    """Helper for advancing time in tests."""
    
    def __init__(self, start_time: Optional[datetime] = None):
        self.current_time = start_time or datetime.now()
        self._patches = []
    
    def advance(self, **kwargs):
        """Advance time by the given timedelta arguments."""
        self.current_time += timedelta(**kwargs)
        return self.current_time
    
    def __enter__(self):
        # Patch datetime.now() to return our controlled time
        patcher = patch('datetime.datetime')
        mock_datetime = patcher.start()
        mock_datetime.now.return_value = self.current_time
        mock_datetime.side_effect = lambda *args, **kwargs: datetime(*args, **kwargs)
        self._patches.append(patcher)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        for patcher in self._patches:
            patcher.stop()


# ─────────────────────────────────────────────────────────────────────────────
# Performance Helpers
# ─────────────────────────────────────────────────────────────────────────────


class PerformanceTimer:
    """Context manager for timing operations in tests."""
    
    def __init__(self, name: str = "Operation"):
        self.name = name
        self.start_time = None
        self.end_time = None
        self.duration = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = datetime.now()
        self.duration = (self.end_time - self.start_time).total_seconds()
        print(f"\n{self.name} took {self.duration:.3f} seconds")
    
    def assert_faster_than(self, seconds: float):
        """Assert the operation completed within the given time."""
        assert self.duration is not None, "Timer not used in context manager"
        assert self.duration < seconds, f"{self.name} took {self.duration:.3f}s, expected < {seconds}s"


# ─────────────────────────────────────────────────────────────────────────────
# Error Helpers
# ─────────────────────────────────────────────────────────────────────────────


def assert_raises_with_message(exception_type, message_pattern, func, *args, **kwargs):
    """Assert a function raises an exception with a message matching the pattern."""
    import re
    
    try:
        func(*args, **kwargs)
        assert False, f"Expected {exception_type.__name__} to be raised"
    except exception_type as e:
        assert re.search(message_pattern, str(e)), \
            f"Exception message '{str(e)}' does not match pattern '{message_pattern}'"
    except Exception as e:
        assert False, f"Expected {exception_type.__name__}, got {type(e).__name__}: {str(e)}"


# ─────────────────────────────────────────────────────────────────────────────
# Integration Test Helpers
# ─────────────────────────────────────────────────────────────────────────────


async def wait_for_condition(
    condition_func,
    timeout: float = 5.0,
    interval: float = 0.1,
    message: str = "Condition not met"
):
    """Wait for a condition to become true within a timeout."""
    start_time = datetime.now()
    
    while (datetime.now() - start_time).total_seconds() < timeout:
        if await condition_func() if asyncio.iscoroutinefunction(condition_func) else condition_func():
            return True
        await asyncio.sleep(interval)
    
    raise TimeoutError(f"{message} after {timeout} seconds")


def create_test_environment(base_dir: Path) -> Dict[str, Path]:
    """Create a complete test environment directory structure."""
    paths = {
        "base": base_dir,
        "screenshots": base_dir / "screenshots",
        "logs": base_dir / "logs",
        "tasks": base_dir / "tasks",
        "config": base_dir / "config",
    }
    
    for path in paths.values():
        path.mkdir(parents=True, exist_ok=True)
    
    # Create some initial files
    (paths["tasks"] / "tasks.json").write_text('{"tasks": []}')
    (paths["config"] / "config.json").write_text('{"version": "1.0"}')
    
    return paths
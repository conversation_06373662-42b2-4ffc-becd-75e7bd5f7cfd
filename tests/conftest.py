"""
Pytest configuration and shared fixtures for the Feisty4 test suite.

This module provides common fixtures and utilities used across all test files.
"""

import json
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import Generator, List
from unittest.mock import Mock, AsyncMock

import pytest

from src.interfaces import (
    AIAnalysisResult,
    EscalationLevel,
    EscalationState,
    JustificationRequest,
    JustificationResponse,
    ScreenshotData,
    TaskData,
    TaskPriority,
    TaskStatus,
)


# ─────────────────────────────────────────────────────────────────────────────
# Directory Fixtures
# ─────────────────────────────────────────────────────────────────────────────


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Provide a temporary directory that's cleaned up after the test."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def mock_storage_dir(temp_dir: Path) -> Path:
    """Create a mock storage directory structure."""
    # Create subdirectories
    (temp_dir / "screenshots").mkdir()
    (temp_dir / "logs").mkdir()
    (temp_dir / "tasks").mkdir()
    
    # Create date folders
    today = datetime.now().strftime("%Y%b%d").upper()
    (temp_dir / "screenshots" / today).mkdir()
    (temp_dir / "logs" / today).mkdir()
    
    return temp_dir


# ─────────────────────────────────────────────────────────────────────────────
# Data Fixtures
# ─────────────────────────────────────────────────────────────────────────────


@pytest.fixture
def sample_screenshot_data() -> ScreenshotData:
    """Provide sample screenshot data."""
    return {
        "path": "/tmp/screenshot_001.png",
        "timestamp": datetime.now().isoformat(),
        "screen_id": 1
    }


@pytest.fixture
def sample_tasks() -> List[TaskData]:
    """Provide a list of sample tasks."""
    base_time = datetime.now()
    return [
        TaskData(
            id="task-001",
            priority=TaskPriority.P1,
            description="Complete unit tests for task manager",
            status=TaskStatus.IN_PROGRESS,
            created_at=base_time - timedelta(hours=2)
        ),
        TaskData(
            id="task-002",
            priority=TaskPriority.P2,
            description="Review pull requests",
            status=TaskStatus.PENDING,
            created_at=base_time - timedelta(hours=1)
        ),
        TaskData(
            id="task-003",
            priority=TaskPriority.P3,
            description="Update documentation",
            status=TaskStatus.COMPLETED,
            created_at=base_time - timedelta(hours=3),
            completed_at=base_time - timedelta(minutes=30)
        ),
        TaskData(
            id="task-004",
            priority=TaskPriority.P1,
            description="Fix critical production bug",
            status=TaskStatus.PENDING,
            created_at=base_time - timedelta(minutes=30)
        ),
        TaskData(
            id="task-005",
            priority=TaskPriority.P4,
            description="Refactor legacy code",
            status=TaskStatus.BLOCKED,
            created_at=base_time - timedelta(hours=4)
        ),
    ]


@pytest.fixture
def sample_analysis_result() -> AIAnalysisResult:
    """Provide sample AI analysis result."""
    return AIAnalysisResult(
        on_task=False,
        confidence=0.85,
        reason="User browsing social media while task is 'Complete unit tests'",
        screenshot_ref="/tmp/screenshot_001.png"
    )


@pytest.fixture
def sample_justification_request() -> JustificationRequest:
    """Provide sample justification request."""
    return JustificationRequest(
        timestamp=datetime.now(),
        detected_activity="Browsing Reddit",
        expected_task="Complete unit tests for task manager"
    )


@pytest.fixture
def sample_justification_response() -> JustificationResponse:
    """Provide sample justification response."""
    return JustificationResponse(
        user_input="Looking up pytest documentation on Reddit",
        valid=True,
        timestamp=datetime.now()
    )


@pytest.fixture
def sample_escalation_state() -> EscalationState:
    """Provide sample escalation state."""
    return EscalationState(
        level=EscalationLevel.WARNING,
        started_at=datetime.now() - timedelta(minutes=10),
        prompt_count=2
    )


# ─────────────────────────────────────────────────────────────────────────────
# Mock Fixtures
# ─────────────────────────────────────────────────────────────────────────────


@pytest.fixture
def mock_screenshot_capture():
    """Mock screenshot capture interface."""
    mock = Mock()
    mock.capture_screenshot = AsyncMock(return_value={
        "path": "/tmp/mock_screenshot.png",
        "timestamp": datetime.now().isoformat(),
        "screen_id": 1
    })
    mock.get_available_screens = Mock(return_value=[1, 2])
    mock.cleanup_old_screenshots = Mock(return_value=5)
    return mock


@pytest.fixture
def mock_task_manager():
    """Mock task manager interface."""
    mock = Mock()
    mock.get_current_task = Mock()
    mock.get_all_tasks = Mock()
    mock.add_task = Mock()
    mock.update_task_status = Mock()
    mock.import_tasks = Mock()
    mock.update_task_order = Mock()
    mock.mark_complete = Mock()
    return mock


@pytest.fixture
def mock_local_store():
    """Mock local storage interface."""
    mock = Mock()
    mock.save_screenshot_metadata = Mock()
    mock.save_analysis_log = Mock()
    mock.save_task_list = Mock()
    mock.load_task_list = Mock()
    mock.get_activity_summary = Mock()
    mock.save_justification_log = Mock()
    mock.list_stored_items = Mock()
    mock.cleanup_old_data = Mock()
    mock.compress_old_screenshots = Mock()
    return mock


@pytest.fixture
def mock_ai_reasoning():
    """Mock AI reasoning interface."""
    mock = Mock()
    mock.analyze_screenshot = AsyncMock()
    mock.validate_justification = AsyncMock()
    mock.get_confidence_threshold = Mock(return_value=0.7)
    mock.set_confidence_threshold = Mock()
    return mock


@pytest.fixture
def mock_user_interface():
    """Mock user interface."""
    mock = Mock()
    mock.prompt_for_justification = AsyncMock()
    mock.show_notification = AsyncMock()
    mock.get_user_command = AsyncMock()
    mock.display_task_list = AsyncMock()
    mock.confirm_action = AsyncMock()
    return mock


@pytest.fixture
def mock_escalator():
    """Mock escalator interface."""
    mock = Mock()
    mock.check_escalation_needed = Mock()
    mock.execute_intervention = AsyncMock()
    mock.update_escalation_state = Mock()
    mock.reset_escalation = Mock()
    mock.get_current_state = Mock()
    return mock


# ─────────────────────────────────────────────────────────────────────────────
# File Creation Helpers
# ─────────────────────────────────────────────────────────────────────────────


@pytest.fixture
def create_test_screenshot(temp_dir: Path):
    """Factory fixture to create test screenshot files."""
    def _create_screenshot(name: str = "test.png", content: bytes = b"PNG_DATA") -> Path:
        screenshot_path = temp_dir / name
        screenshot_path.write_bytes(content)
        return screenshot_path
    return _create_screenshot


@pytest.fixture
def create_test_jsonl(temp_dir: Path):
    """Factory fixture to create test JSONL files."""
    def _create_jsonl(name: str, entries: List[dict]) -> Path:
        jsonl_path = temp_dir / name
        with open(jsonl_path, 'w') as f:
            for entry in entries:
                json.dump(entry, f)
                f.write('\n')
        return jsonl_path
    return _create_jsonl


# ─────────────────────────────────────────────────────────────────────────────
# Time Fixtures
# ─────────────────────────────────────────────────────────────────────────────


@pytest.fixture
def frozen_time():
    """Mock datetime for consistent time-based tests."""
    # This would typically use freezegun or similar
    # For now, we'll just return a fixed datetime
    return datetime(2025, 6, 15, 10, 30, 0)


# ─────────────────────────────────────────────────────────────────────────────
# Configuration Fixtures
# ─────────────────────────────────────────────────────────────────────────────


@pytest.fixture
def test_config():
    """Provide test configuration dictionary."""
    return {
        "storage_path": "/tmp/test_storage",
        "screenshot_interval": 30,
        "max_storage_days": 7,
        "confidence_threshold": 0.7,
        "escalation_thresholds": {
            "warning": 3,
            "frequent": 5,
            "aggressive": 10
        }
    }
"""
Comprehensive tests for the Escalator module.

Tests cover:
- Escalation logic and state transitions
- De-escalation based on good behavior
- Justification validation
- Intervention determination
- Prompt tracking
- Integration with other components
"""

import asyncio
import platform
import subprocess
from datetime import datetime, timed<PERSON><PERSON>
from typing import List
from unittest.mock import Mock, patch, call

import pytest

from src.interfaces import (
    AIAnalysisResult,
    EscalationLevel,
    EscalationState,
    JustificationRequest,
    JustificationResponse,
)
from src.escalator.config import EscalatorConfig
from src.escalator.escalator import Escalator, InterventionAction, PromptRecord
from tests.test_utils import generate_analysis_results, TimeAdvancer


class TestEscalator:
    """Test suite for Escalator class."""
    
    # ─────────────────────────────────────────────────────────────────────────
    # Initialization Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_init_default_config(self):
        """Test initialization with default configuration."""
        escalator = Escalator()
        
        assert escalator.config is not None
        assert isinstance(escalator.config, EscalatorConfig)
        assert escalator._current_state.level == EscalationLevel.NORMAL
        assert escalator._current_state.prompt_count == 0
        assert escalator._consecutive_on_task_start is None
        assert len(escalator._prompt_history) == 0
        assert len(escalator._off_task_history) == 0
    
    def test_init_custom_config(self):
        """Test initialization with custom configuration."""
        config = EscalatorConfig(
            prompts_before_escalation=5,
            de_escalation_time_minutes=20,
            max_ignored_prompts=4,
            min_justification_length=15
        )
        escalator = Escalator(config=config)
        
        assert escalator.config == config
        assert escalator.config.prompts_before_escalation == 5
        assert escalator.config.de_escalation_time_minutes == 20
    
    def test_get_current_state(self):
        """Test getting current escalation state."""
        escalator = Escalator()
        
        state = escalator.get_current_state()
        assert state.level == EscalationLevel.NORMAL
        assert state.prompt_count == 0
        assert isinstance(state.started_at, datetime)
    
    # ─────────────────────────────────────────────────────────────────────────
    # Justification Validation Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_check_justification_valid(self):
        """Test valid justification checking."""
        escalator = Escalator()
        
        # Sufficient length
        assert escalator.check_justification("Looking up documentation for the task") is True
        
        # Just meets minimum length
        assert escalator.check_justification("a" * 10) is True
    
    def test_check_justification_too_short(self):
        """Test justification that's too short."""
        escalator = Escalator()
        
        assert escalator.check_justification("Break") is False
        assert escalator.check_justification("") is False
        assert escalator.check_justification("   ") is False
    
    def test_check_justification_task_relevance_required(self):
        """Test justification with task relevance requirement."""
        config = EscalatorConfig(require_task_relevance=True)
        escalator = Escalator(config=config)
        
        current_task = "Implement user authentication module"
        
        # Mentions task keywords
        assert escalator.check_justification(
            "Looking up authentication best practices",
            current_task
        ) is True
        
        # Doesn't mention task
        assert escalator.check_justification(
            "Taking a quick break to stretch",
            current_task
        ) is False
    
    def test_check_justification_task_relevance_not_required(self):
        """Test justification without task relevance requirement."""
        config = EscalatorConfig(require_task_relevance=False)
        escalator = Escalator(config=config)
        
        current_task = "Write unit tests"
        
        # Should pass even without mentioning task
        assert escalator.check_justification(
            "Checking important email from manager",
            current_task
        ) is True
    
    # ─────────────────────────────────────────────────────────────────────────
    # Process Analysis Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_process_analysis_on_task(self):
        """Test processing on-task analysis."""
        escalator = Escalator()
        
        analysis = AIAnalysisResult(
            on_task=True,
            confidence=0.9,
            reason="User coding in IDE",
            screenshot_ref="/tmp/screenshot.png"
        )
        
        state = escalator.process_analysis(analysis)
        
        assert state.level == EscalationLevel.NORMAL
        assert state.prompt_count == 0
        assert escalator._consecutive_on_task_start is not None
    
    def test_process_analysis_off_task_first_time(self):
        """Test processing first off-task analysis."""
        escalator = Escalator()
        
        analysis = AIAnalysisResult(
            on_task=False,
            confidence=0.85,
            reason="User browsing Reddit",
            screenshot_ref="/tmp/screenshot.png"
        )
        
        state = escalator.process_analysis(analysis)
        
        assert state.level == EscalationLevel.NORMAL  # Not escalated yet
        assert state.prompt_count == 1
        assert escalator._consecutive_on_task_start is None
        assert len(escalator._off_task_history) == 1
    
    def test_process_analysis_off_task_with_valid_justification(self):
        """Test processing off-task with valid justification."""
        escalator = Escalator()
        
        analysis = AIAnalysisResult(
            on_task=False,
            confidence=0.85,
            reason="User on Stack Overflow",
            screenshot_ref="/tmp/screenshot.png"
        )
        
        justification = JustificationResponse(
            user_input="Looking up solution for authentication bug",
            valid=True,
            timestamp=datetime.now()
        )
        
        state = escalator.process_analysis(analysis, justification)
        
        # Should not increment prompt count for valid justification
        assert state.level == EscalationLevel.NORMAL
        assert state.prompt_count == 0  # Not incremented
    
    def test_process_analysis_off_task_with_invalid_justification(self):
        """Test processing off-task with invalid justification."""
        escalator = Escalator()
        
        analysis = AIAnalysisResult(
            on_task=False,
            confidence=0.85,
            reason="User gaming",
            screenshot_ref="/tmp/screenshot.png"
        )
        
        justification = JustificationResponse(
            user_input="Break",
            valid=False,
            timestamp=datetime.now()
        )
        
        state = escalator.process_analysis(analysis, justification)
        
        # Should increment prompt count for invalid justification
        assert state.prompt_count == 1
    
    def test_process_analysis_triggers_escalation(self):
        """Test that repeated off-task behavior triggers escalation."""
        config = EscalatorConfig(prompts_before_escalation=3)
        escalator = Escalator(config=config)
        
        analysis = AIAnalysisResult(
            on_task=False,
            confidence=0.85,
            reason="User off-task",
            screenshot_ref="/tmp/screenshot.png"
        )
        
        # Process 3 off-task analyses
        for i in range(3):
            state = escalator.process_analysis(analysis)
        
        # Should have escalated after 3rd prompt
        assert state.level == EscalationLevel.WARNING
        assert state.prompt_count == 0  # Reset after escalation
    
    # ─────────────────────────────────────────────────────────────────────────
    # Escalation/De-escalation Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_escalate_progression(self):
        """Test escalation through all levels."""
        escalator = Escalator()
        
        # Start at NORMAL
        assert escalator._current_state.level == EscalationLevel.NORMAL
        
        # Escalate to WARNING
        state = escalator.escalate()
        assert state.level == EscalationLevel.WARNING
        assert state.prompt_count == 0
        
        # Escalate to FREQUENT
        state = escalator.escalate()
        assert state.level == EscalationLevel.FREQUENT
        
        # Escalate to AGGRESSIVE
        state = escalator.escalate()
        assert state.level == EscalationLevel.AGGRESSIVE
        
        # Try to escalate beyond max
        state = escalator.escalate()
        assert state.level == EscalationLevel.AGGRESSIVE  # Should stay at max
    
    def test_de_escalate_progression(self):
        """Test de-escalation through levels."""
        escalator = Escalator()
        
        # Set to AGGRESSIVE
        escalator._current_state = EscalationState(
            level=EscalationLevel.AGGRESSIVE,
            started_at=datetime.now(),
            prompt_count=0
        )
        
        # De-escalate to FREQUENT
        state = escalator.de_escalate()
        assert state.level == EscalationLevel.FREQUENT
        assert state.prompt_count == 0
        
        # De-escalate to WARNING
        state = escalator.de_escalate()
        assert state.level == EscalationLevel.WARNING
        
        # De-escalate to NORMAL
        state = escalator.de_escalate()
        assert state.level == EscalationLevel.NORMAL
        
        # Try to de-escalate below minimum
        state = escalator.de_escalate()
        assert state.level == EscalationLevel.NORMAL  # Should stay at min
    
    def test_auto_de_escalation_on_good_behavior(self):
        """Test automatic de-escalation after sustained on-task behavior."""
        config = EscalatorConfig(de_escalation_time_minutes=10)
        escalator = Escalator(config=config)
        
        # Set to WARNING level
        escalator._current_state = EscalationState(
            level=EscalationLevel.WARNING,
            started_at=datetime.now(),
            prompt_count=0
        )
        
        # First on-task analysis starts timer
        on_task_analysis = AIAnalysisResult(
            on_task=True,
            confidence=0.9,
            reason="User coding",
            screenshot_ref="/tmp/screenshot.png"
        )
        
        escalator.process_analysis(on_task_analysis)
        assert escalator._current_state.level == EscalationLevel.WARNING
        
        # Simulate 10 minutes passing
        escalator._consecutive_on_task_start = datetime.now() - timedelta(minutes=11)
        
        # Next on-task analysis should trigger de-escalation
        state = escalator.process_analysis(on_task_analysis)
        assert state.level == EscalationLevel.NORMAL
    
    def test_de_escalation_timer_reset_on_off_task(self):
        """Test that de-escalation timer resets when going off-task."""
        escalator = Escalator()
        
        # Start on-task timer
        on_task = AIAnalysisResult(
            on_task=True,
            confidence=0.9,
            reason="Working",
            screenshot_ref="/tmp/1.png"
        )
        escalator.process_analysis(on_task)
        assert escalator._consecutive_on_task_start is not None
        
        # Go off-task
        off_task = AIAnalysisResult(
            on_task=False,
            confidence=0.85,
            reason="Distracted",
            screenshot_ref="/tmp/2.png"
        )
        escalator.process_analysis(off_task)
        
        # Timer should be reset
        assert escalator._consecutive_on_task_start is None
    
    # ─────────────────────────────────────────────────────────────────────────
    # Intervention Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_should_intervene_normal_mode(self):
        """Test intervention logic in normal mode."""
        escalator = Escalator()
        
        assert escalator.should_intervene() is False
        
        # Even with unanswered prompts in NORMAL mode
        escalator._prompt_history = [
            PromptRecord(
                timestamp=datetime.now(),
                request=Mock(),
                was_answered=False
            )
        ]
        assert escalator.should_intervene() is False
    
    def test_should_intervene_max_ignored_prompts(self):
        """Test intervention when max ignored prompts exceeded."""
        config = EscalatorConfig(max_ignored_prompts=3)
        escalator = Escalator(config=config)
        
        # Add 3 unanswered prompts
        for i in range(3):
            escalator._prompt_history.append(
                PromptRecord(
                    timestamp=datetime.now() - timedelta(minutes=i),
                    request=Mock(),
                    was_answered=False
                )
            )
        
        assert escalator.should_intervene() is True
    
    def test_should_intervene_aggressive_mode(self):
        """Test intervention logic in aggressive mode."""
        escalator = Escalator()
        escalator._current_state = EscalationState(
            level=EscalationLevel.AGGRESSIVE,
            started_at=datetime.now(),
            prompt_count=0
        )
        
        # Should not intervene with no unanswered prompts
        assert escalator.should_intervene() is False
        
        # Should intervene with any unanswered prompt
        escalator._prompt_history.append(
            PromptRecord(
                timestamp=datetime.now(),
                request=Mock(),
                was_answered=False
            )
        )
        assert escalator.should_intervene() is True
    
    def test_get_intervention_action_by_level(self):
        """Test intervention action determination by escalation level."""
        escalator = Escalator()
        
        # NORMAL - no intervention
        escalator._current_state.level = EscalationLevel.NORMAL
        assert escalator.get_intervention_action() == InterventionAction.NONE
        
        # WARNING - notify
        escalator._current_state.level = EscalationLevel.WARNING
        assert escalator.get_intervention_action() == InterventionAction.NOTIFY
        
        # FREQUENT - frequent check
        escalator._current_state.level = EscalationLevel.FREQUENT
        assert escalator.get_intervention_action() == InterventionAction.FREQUENT_CHECK
        
        # AGGRESSIVE - depends on ignored prompts
        escalator._current_state.level = EscalationLevel.AGGRESSIVE
        assert escalator.get_intervention_action() == InterventionAction.NOTIFY
        
        # AGGRESSIVE with 3+ ignored prompts
        for i in range(3):
            escalator._prompt_history.append(
                PromptRecord(
                    timestamp=datetime.now(),
                    request=Mock(),
                    was_answered=False
                )
            )
        assert escalator.get_intervention_action() == InterventionAction.CLOSE_TABS
    
    @patch('subprocess.run')
    def test_execute_intervention_close_tabs(self, mock_run):
        """Test tab closing intervention execution."""
        escalator = Escalator()
        escalator._current_state.level = EscalationLevel.AGGRESSIVE
        
        # Add enough unanswered prompts
        for i in range(3):
            escalator._prompt_history.append(
                PromptRecord(
                    timestamp=datetime.now(),
                    request=Mock(),
                    was_answered=False
                )
            )
        
        escalator.execute_intervention()
        
        # Should have attempted to close tabs
        assert mock_run.called
    
    def test_execute_intervention_notify(self):
        """Test notification intervention."""
        escalator = Escalator()
        escalator._current_state.level = EscalationLevel.WARNING
        
        # Should not raise any errors
        escalator.execute_intervention()
    
    # ─────────────────────────────────────────────────────────────────────────
    # Prompt Tracking Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_get_unanswered_prompt_count(self):
        """Test counting unanswered prompts."""
        escalator = Escalator()
        
        # No prompts
        assert escalator.get_unanswered_prompt_count() == 0
        
        # Mix of answered and unanswered
        escalator._prompt_history = [
            PromptRecord(datetime.now(), Mock(), was_answered=True),
            PromptRecord(datetime.now(), Mock(), was_answered=False),
            PromptRecord(datetime.now(), Mock(), was_answered=True),
            PromptRecord(datetime.now(), Mock(), was_answered=False),
            PromptRecord(datetime.now(), Mock(), was_answered=False),
        ]
        
        assert escalator.get_unanswered_prompt_count() == 3
    
    def test_recent_unanswered_prompts(self):
        """Test getting recent unanswered prompts."""
        escalator = Escalator()
        
        # Add prompts at different times
        now = datetime.now()
        escalator._prompt_history = [
            PromptRecord(now - timedelta(minutes=30), Mock(), was_answered=False),
            PromptRecord(now - timedelta(minutes=20), Mock(), was_answered=True),
            PromptRecord(now - timedelta(minutes=10), Mock(), was_answered=False),
            PromptRecord(now - timedelta(minutes=5), Mock(), was_answered=False),
        ]
        
        # Get prompts from last 15 minutes
        recent = escalator.get_recent_unanswered_prompts(minutes=15)
        assert len(recent) == 2
    
    def test_prompt_response_tracking(self):
        """Test tracking prompt responses."""
        escalator = Escalator()
        
        # Add unanswered prompt
        request = JustificationRequest(
            timestamp=datetime.now(),
            detected_activity="Browsing",
            expected_task="Coding"
        )
        prompt = PromptRecord(datetime.now(), request)
        escalator._prompt_history.append(prompt)
        
        # Process with justification
        analysis = AIAnalysisResult(
            on_task=False,
            confidence=0.85,
            reason="Off-task",
            screenshot_ref="/tmp/screenshot.png"
        )
        
        justification = JustificationResponse(
            user_input="Taking a break",
            valid=True,
            timestamp=datetime.now()
        )
        
        escalator.process_analysis(analysis, justification)
        
        # Should update the prompt record
        assert escalator._prompt_history[-1].was_answered is True
        assert escalator._prompt_history[-1].response == justification
    
    # ─────────────────────────────────────────────────────────────────────────
    # Reset Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_reset_escalation(self):
        """Test resetting escalation state."""
        escalator = Escalator()
        
        # Set up escalated state with history
        escalator._current_state = EscalationState(
            level=EscalationLevel.AGGRESSIVE,
            started_at=datetime.now() - timedelta(hours=2),
            prompt_count=5
        )
        
        # Add various history
        now = datetime.now()
        escalator._off_task_history = [
            (now - timedelta(hours=2), Mock()),
            (now - timedelta(minutes=30), Mock()),
            (now - timedelta(minutes=10), Mock()),
        ]
        
        escalator._prompt_history = [
            PromptRecord(now - timedelta(hours=2), Mock()),
            PromptRecord(now - timedelta(minutes=30), Mock()),
        ]
        
        # Reset
        escalator.reset_escalation()
        
        # Should be back to normal
        assert escalator._current_state.level == EscalationLevel.NORMAL
        assert escalator._current_state.prompt_count == 0
        assert escalator._consecutive_on_task_start is None
        
        # Should keep only recent history (last hour)
        assert len(escalator._off_task_history) == 2  # Last 2 within hour
        assert len(escalator._prompt_history) == 1  # Last 1 within hour
    
    # ─────────────────────────────────────────────────────────────────────────
    # Analytics Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_get_statistics(self):
        """Test getting escalation statistics."""
        escalator = Escalator()
        
        # Add some history
        now = datetime.now()
        for i in range(5):
            escalator._off_task_history.append(
                (now - timedelta(minutes=i*10), Mock())
            )
        
        for i in range(3):
            escalator._prompt_history.append(
                PromptRecord(
                    now - timedelta(minutes=i*15),
                    Mock(),
                    was_answered=(i % 2 == 0)
                )
            )
        
        stats = escalator.get_statistics()
        
        assert stats["current_level"] == "NORMAL"
        assert stats["off_task_incidents"] == 5
        assert stats["total_prompts"] == 3
        assert stats["answered_prompts"] == 2
        assert stats["unanswered_prompts"] == 1
        assert isinstance(stats["time_in_current_level"], str)


# ─────────────────────────────────────────────────────────────────────────────
# Integration Tests
# ─────────────────────────────────────────────────────────────────────────────


class TestEscalatorIntegration:
    """Integration tests for Escalator with full workflows."""
    
    def test_complete_escalation_workflow(self):
        """Test complete escalation workflow from normal to aggressive."""
        config = EscalatorConfig(
            prompts_before_escalation=2,
            de_escalation_time_minutes=5
        )
        escalator = Escalator(config=config)
        
        off_task = AIAnalysisResult(
            on_task=False,
            confidence=0.85,
            reason="Distracted",
            screenshot_ref="/tmp/screenshot.png"
        )
        
        # Initial state
        assert escalator.get_current_state().level == EscalationLevel.NORMAL
        
        # First 2 off-task -> escalate to WARNING
        for _ in range(2):
            escalator.process_analysis(off_task)
        assert escalator.get_current_state().level == EscalationLevel.WARNING
        
        # Next 2 off-task -> escalate to FREQUENT
        for _ in range(2):
            escalator.process_analysis(off_task)
        assert escalator.get_current_state().level == EscalationLevel.FREQUENT
        
        # Next 2 off-task -> escalate to AGGRESSIVE
        for _ in range(2):
            escalator.process_analysis(off_task)
        assert escalator.get_current_state().level == EscalationLevel.AGGRESSIVE
        
        # Intervention should be recommended
        assert escalator.get_intervention_action() != InterventionAction.NONE
    
    def test_de_escalation_workflow(self):
        """Test de-escalation workflow with sustained good behavior."""
        config = EscalatorConfig(de_escalation_time_minutes=2)
        escalator = Escalator(config=config)
        
        # Set to WARNING level
        escalator._current_state = EscalationState(
            level=EscalationLevel.WARNING,
            started_at=datetime.now(),
            prompt_count=0
        )
        
        on_task = AIAnalysisResult(
            on_task=True,
            confidence=0.9,
            reason="Working",
            screenshot_ref="/tmp/screenshot.png"
        )
        
        with TimeAdvancer() as time_advancer:
            # First on-task starts timer
            escalator.process_analysis(on_task)
            assert escalator.get_current_state().level == EscalationLevel.WARNING
            
            # Advance time and check again
            time_advancer.advance(minutes=3)
            escalator._consecutive_on_task_start = time_advancer.current_time - timedelta(minutes=3)
            
            escalator.process_analysis(on_task)
            assert escalator.get_current_state().level == EscalationLevel.NORMAL
    
    def test_mixed_behavior_workflow(self):
        """Test workflow with mixed on-task and off-task behavior."""
        config = EscalatorConfig(prompts_before_escalation=3)
        escalator = Escalator(config=config)
        
        on_task = AIAnalysisResult(
            on_task=True,
            confidence=0.9,
            reason="Working",
            screenshot_ref="/tmp/on.png"
        )
        
        off_task = AIAnalysisResult(
            on_task=False,
            confidence=0.85,
            reason="Distracted",
            screenshot_ref="/tmp/off.png"
        )
        
        # Pattern: off, off, on, off, off, off
        escalator.process_analysis(off_task)  # count = 1
        escalator.process_analysis(off_task)  # count = 2
        escalator.process_analysis(on_task)   # doesn't reset count
        escalator.process_analysis(off_task)  # count = 3, escalate!
        
        assert escalator.get_current_state().level == EscalationLevel.WARNING
        
        # Continue pattern
        escalator.process_analysis(off_task)  # count = 1 (in WARNING)
        escalator.process_analysis(off_task)  # count = 2
        escalator.process_analysis(off_task)  # count = 3, escalate!
        
        assert escalator.get_current_state().level == EscalationLevel.FREQUENT


# ─────────────────────────────────────────────────────────────────────────────
# Main execution
# ─────────────────────────────────────────────────────────────────────────────


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
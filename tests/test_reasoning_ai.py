"""
Comprehensive tests for the Reasoning AI module.

Tests cover:
- Mock mode analysis
- Confidence scoring
- OpenAI API integration (mocked)
- Error handling
- Prompt generation
- Response parsing
"""

import asyncio
import base64
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any
from unittest.mock import Mock, patch, AsyncMock, MagicMock, mock_open

import pytest

from src.interfaces import (
    AIAnalysisResult,
    ScreenshotData,
    TaskData,
    TaskPriority,
    TaskStatus,
)
from src.reasoning_ai.config import ReasoningConfig
from src.reasoning_ai.reasoner import ReasoningAI
from tests.test_utils import (
    async_return,
    async_raise,
    generate_tasks,
)


class TestReasoningAI:
    """Test suite for ReasoningAI class."""
    
    # ─────────────────────────────────────────────────────────────────────────
    # Initialization Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_init_default_config(self):
        """Test initialization with default configuration."""
        reasoner = ReasoningAI()
        
        assert reasoner.config is not None
        assert isinstance(reasoner.config, ReasoningConfig)
        assert reasoner.config.mock_mode is True  # Default is mock mode
        assert reasoner._client is None  # No OpenAI client in mock mode
    
    def test_init_custom_config(self):
        """Test initialization with custom configuration."""
        config = ReasoningConfig(
            mock_mode=True,
            confidence_threshold=0.8,
            model_name="gpt-4-vision-preview",
            temperature=0.5
        )
        reasoner = ReasoningAI(config=config)
        
        assert reasoner.config == config
        assert reasoner.config.confidence_threshold == 0.8
        assert reasoner.config.temperature == 0.5
    
    @patch('openai.OpenAI')
    def test_init_openai_mode(self, mock_openai_class):
        """Test initialization in OpenAI mode."""
        config = ReasoningConfig(
            mock_mode=False,
            openai_api_key="test-key-123"
        )
        
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        reasoner = ReasoningAI(config=config)
        
        assert reasoner._client == mock_client
        mock_openai_class.assert_called_once_with(api_key="test-key-123")
    
    def test_init_openai_import_error(self):
        """Test initialization handles missing OpenAI package."""
        config = ReasoningConfig(mock_mode=False)
        
        with patch.dict('sys.modules', {'openai': None}):
            with pytest.raises(ImportError):
                ReasoningAI(config=config)
    
    # ─────────────────────────────────────────────────────────────────────────
    # Mock Analysis Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    @pytest.mark.asyncio
    async def test_mock_analyze_basic(self, sample_screenshot_data, sample_tasks):
        """Test basic mock analysis."""
        reasoner = ReasoningAI()
        task = sample_tasks[0]
        
        result = await reasoner.analyze_screenshot(sample_screenshot_data, task)
        
        assert isinstance(result, AIAnalysisResult)
        assert isinstance(result.on_task, bool)
        assert 0.0 <= result.confidence <= 1.0
        assert len(result.reason) > 0
        assert result.screenshot_ref == sample_screenshot_data["path"]
        assert task.description in result.reason
    
    @pytest.mark.asyncio
    async def test_mock_analyze_random_behavior(self, sample_screenshot_data, sample_tasks):
        """Test mock analysis produces varied results."""
        reasoner = ReasoningAI()
        task = sample_tasks[0]
        
        results = []
        for _ in range(20):
            result = await reasoner.analyze_screenshot(sample_screenshot_data, task)
            results.append(result)
        
        # Should have both on-task and off-task results
        on_task_count = sum(1 for r in results if r.on_task)
        off_task_count = sum(1 for r in results if not r.on_task)
        
        assert on_task_count > 0
        assert off_task_count > 0
        
        # Should have varied confidence levels
        confidences = [r.confidence for r in results]
        assert len(set(confidences)) > 1
    
    @pytest.mark.asyncio
    async def test_mock_analyze_contextual_reasons(self, sample_screenshot_data):
        """Test mock analysis provides contextual reasons."""
        reasoner = ReasoningAI()
        
        task = TaskData(
            id="test-001",
            priority=TaskPriority.P1,
            description="Fix authentication bug in login module",
            status=TaskStatus.IN_PROGRESS,
            created_at=datetime.now()
        )
        
        result = await reasoner.analyze_screenshot(sample_screenshot_data, task)
        
        # Reason should mention the task
        assert "Fix authentication bug in login module" in result.reason
        
        if result.on_task:
            assert "working on" in result.reason
        else:
            assert "instead of" in result.reason
    
    @pytest.mark.asyncio
    async def test_mock_analyze_timing(self, sample_screenshot_data, sample_tasks):
        """Test mock analysis has simulated delay."""
        reasoner = ReasoningAI()
        task = sample_tasks[0]
        
        start_time = asyncio.get_event_loop().time()
        await reasoner.analyze_screenshot(sample_screenshot_data, task)
        end_time = asyncio.get_event_loop().time()
        
        # Should have ~0.5 second delay
        elapsed = end_time - start_time
        assert 0.4 < elapsed < 0.6
    
    # ─────────────────────────────────────────────────────────────────────────
    # OpenAI Analysis Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    @pytest.mark.asyncio
    @patch('openai.OpenAI')
    async def test_openai_analyze_success(self, mock_openai_class, temp_dir, sample_tasks):
        """Test successful OpenAI analysis."""
        # Setup mock OpenAI client
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        # Mock response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "on_task": True,
            "confidence": 0.92,
            "detected_activity": "User coding in VS Code",
            "reasoning": "The IDE shows code related to authentication"
        })
        
        mock_client.chat.completions.create.return_value = mock_response
        
        # Create test screenshot
        screenshot_path = temp_dir / "test_screenshot.png"
        screenshot_path.write_bytes(b"PNG_IMAGE_DATA")
        
        screenshot_data: ScreenshotData = {
            "path": str(screenshot_path),
            "timestamp": datetime.now().isoformat(),
            "screen_id": 1
        }
        
        # Test analysis
        config = ReasoningConfig(mock_mode=False, openai_api_key="test-key")
        reasoner = ReasoningAI(config=config)
        
        result = await reasoner.analyze_screenshot(screenshot_data, sample_tasks[0])
        
        assert result.on_task is True
        assert result.confidence == 0.92
        assert "User coding in VS Code" in result.reason
        assert "authentication" in result.reason
        
        # Verify API call
        mock_client.chat.completions.create.assert_called_once()
        call_args = mock_client.chat.completions.create.call_args
        assert call_args.kwargs["model"] == config.model_name
        assert call_args.kwargs["max_tokens"] == config.max_tokens
        assert call_args.kwargs["temperature"] == config.temperature
    
    @pytest.mark.asyncio
    @patch('openai.OpenAI')
    async def test_openai_analyze_file_not_found(self, mock_openai_class, sample_tasks, caplog):
        """Test OpenAI analysis with missing screenshot file."""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        screenshot_data: ScreenshotData = {
            "path": "/nonexistent/screenshot.png",
            "timestamp": datetime.now().isoformat(),
            "screen_id": 1
        }
        
        config = ReasoningConfig(mock_mode=False, openai_api_key="test-key")
        reasoner = ReasoningAI(config=config)
        
        # Should fallback to mock
        result = await reasoner.analyze_screenshot(screenshot_data, sample_tasks[0])
        
        assert isinstance(result, AIAnalysisResult)
        assert "OpenAI analysis failed" in caplog.text
        assert "Screenshot not found" in caplog.text
    
    @pytest.mark.asyncio
    @patch('openai.OpenAI')
    async def test_openai_analyze_api_error(self, mock_openai_class, temp_dir, sample_tasks, caplog):
        """Test OpenAI analysis handles API errors."""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        mock_client.chat.completions.create.side_effect = Exception("API rate limit exceeded")
        
        # Create test screenshot
        screenshot_path = temp_dir / "test_screenshot.png"
        screenshot_path.write_bytes(b"PNG_DATA")
        
        screenshot_data: ScreenshotData = {
            "path": str(screenshot_path),
            "timestamp": datetime.now().isoformat(),
            "screen_id": 1
        }
        
        config = ReasoningConfig(mock_mode=False, openai_api_key="test-key")
        reasoner = ReasoningAI(config=config)
        
        # Should fallback to mock
        result = await reasoner.analyze_screenshot(screenshot_data, sample_tasks[0])
        
        assert isinstance(result, AIAnalysisResult)
        assert "OpenAI analysis failed" in caplog.text
        assert "API rate limit exceeded" in caplog.text
    
    @pytest.mark.asyncio
    @patch('openai.OpenAI')
    async def test_openai_analyze_invalid_response(self, mock_openai_class, temp_dir, sample_tasks):
        """Test OpenAI analysis handles invalid JSON response."""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        # Mock invalid response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "This is not valid JSON"
        
        mock_client.chat.completions.create.return_value = mock_response
        
        # Create test screenshot
        screenshot_path = temp_dir / "test_screenshot.png"
        screenshot_path.write_bytes(b"PNG_DATA")
        
        screenshot_data: ScreenshotData = {
            "path": str(screenshot_path),
            "timestamp": datetime.now().isoformat(),
            "screen_id": 1
        }
        
        config = ReasoningConfig(mock_mode=False, openai_api_key="test-key")
        reasoner = ReasoningAI(config=config)
        
        result = await reasoner.analyze_screenshot(screenshot_data, sample_tasks[0])
        
        # Should return safe default
        assert result.on_task is True  # Err on side of not interrupting
        assert result.confidence == 0.5
        assert "parsing error" in result.reason
    
    # ─────────────────────────────────────────────────────────────────────────
    # Prompt Building Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_build_analysis_prompt(self):
        """Test analysis prompt generation."""
        reasoner = ReasoningAI()
        
        task = TaskData(
            id="test-001",
            priority=TaskPriority.P1,
            description="Implement OAuth2 authentication flow",
            status=TaskStatus.IN_PROGRESS,
            created_at=datetime.now()
        )
        
        prompt = reasoner._build_analysis_prompt(task)
        
        assert "Implement OAuth2 authentication flow" in prompt
        assert "Current task:" in prompt
        assert "on_task" in prompt
        assert "confidence" in prompt
        assert "detected_activity" in prompt
        assert "reasoning" in prompt
        assert "JSON format" in prompt
    
    # ─────────────────────────────────────────────────────────────────────────
    # Response Parsing Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_parse_openai_response_valid(self):
        """Test parsing valid OpenAI response."""
        reasoner = ReasoningAI()
        
        response_text = json.dumps({
            "on_task": False,
            "confidence": 0.87,
            "detected_activity": "Browsing Reddit",
            "reasoning": "User has Reddit open in browser, no code editor visible"
        })
        
        result = reasoner._parse_openai_response(response_text, "/tmp/screenshot.png")
        
        assert result.on_task is False
        assert result.confidence == 0.87
        assert "Browsing Reddit" in result.reason
        assert "no code editor visible" in result.reason
        assert result.screenshot_ref == "/tmp/screenshot.png"
    
    def test_parse_openai_response_missing_fields(self):
        """Test parsing response with missing fields."""
        reasoner = ReasoningAI()
        
        response_text = json.dumps({
            "on_task": True,
            # Missing confidence and other fields
        })
        
        result = reasoner._parse_openai_response(response_text, "/tmp/screenshot.png")
        
        assert result.on_task is True
        assert result.confidence == 0.5  # Default
        assert "Unknown activity" in result.reason
    
    def test_parse_openai_response_invalid_json(self, caplog):
        """Test parsing invalid JSON response."""
        reasoner = ReasoningAI()
        
        response_text = "Not valid JSON { broken"
        
        result = reasoner._parse_openai_response(response_text, "/tmp/screenshot.png")
        
        assert result.on_task is True  # Safe default
        assert result.confidence == 0.5
        assert "parsing error" in result.reason
        assert "Failed to parse OpenAI response" in caplog.text
    
    def test_parse_openai_response_wrong_types(self):
        """Test parsing response with wrong data types."""
        reasoner = ReasoningAI()
        
        response_text = json.dumps({
            "on_task": "yes",  # Should be boolean
            "confidence": "high",  # Should be float
            "detected_activity": "Coding",
            "reasoning": "Working hard"
        })
        
        result = reasoner._parse_openai_response(response_text, "/tmp/screenshot.png")
        
        # Should handle gracefully
        assert isinstance(result.on_task, bool)
        assert isinstance(result.confidence, float)
    
    # ─────────────────────────────────────────────────────────────────────────
    # Confidence Threshold Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_set_confidence_threshold_valid(self):
        """Test setting valid confidence threshold."""
        reasoner = ReasoningAI()
        
        reasoner.set_confidence_threshold(0.85)
        assert reasoner.config.confidence_threshold == 0.85
        
        reasoner.set_confidence_threshold(0.0)
        assert reasoner.config.confidence_threshold == 0.0
        
        reasoner.set_confidence_threshold(1.0)
        assert reasoner.config.confidence_threshold == 1.0
    
    def test_set_confidence_threshold_invalid(self):
        """Test setting invalid confidence threshold."""
        reasoner = ReasoningAI()
        
        with pytest.raises(ValueError) as exc_info:
            reasoner.set_confidence_threshold(1.5)
        assert "between 0 and 1" in str(exc_info.value)
        
        with pytest.raises(ValueError):
            reasoner.set_confidence_threshold(-0.1)
    
    # ─────────────────────────────────────────────────────────────────────────
    # Integration Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    @pytest.mark.asyncio
    async def test_multiple_analyses(self, sample_screenshot_data):
        """Test multiple analyses in succession."""
        reasoner = ReasoningAI()
        tasks = generate_tasks(5)
        
        results = []
        for task in tasks:
            result = await reasoner.analyze_screenshot(sample_screenshot_data, task)
            results.append(result)
        
        assert len(results) == 5
        assert all(isinstance(r, AIAnalysisResult) for r in results)
        
        # Each result should reference the correct task
        for task, result in zip(tasks, results):
            assert task.description in result.reason
    
    @pytest.mark.asyncio
    @patch('openai.OpenAI')
    async def test_openai_with_base64_encoding(self, mock_openai_class, temp_dir, sample_tasks):
        """Test OpenAI analysis properly encodes image to base64."""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        # Mock response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "on_task": True,
            "confidence": 0.9,
            "detected_activity": "Coding",
            "reasoning": "Active development"
        })
        
        mock_client.chat.completions.create.return_value = mock_response
        
        # Create test screenshot with specific content
        screenshot_content = b"TEST_PNG_CONTENT_12345"
        screenshot_path = temp_dir / "test_screenshot.png"
        screenshot_path.write_bytes(screenshot_content)
        
        screenshot_data: ScreenshotData = {
            "path": str(screenshot_path),
            "timestamp": datetime.now().isoformat(),
            "screen_id": 1
        }
        
        config = ReasoningConfig(mock_mode=False, openai_api_key="test-key")
        reasoner = ReasoningAI(config=config)
        
        await reasoner.analyze_screenshot(screenshot_data, sample_tasks[0])
        
        # Verify base64 encoding in API call
        call_args = mock_client.chat.completions.create.call_args
        messages = call_args.kwargs["messages"]
        image_content = messages[0]["content"][1]["image_url"]["url"]
        
        expected_base64 = base64.b64encode(screenshot_content).decode("utf-8")
        assert f"data:image/png;base64,{expected_base64}" == image_content


# ─────────────────────────────────────────────────────────────────────────────
# Performance Tests
# ─────────────────────────────────────────────────────────────────────────────


class TestReasoningAIPerformance:
    """Performance tests for ReasoningAI."""
    
    @pytest.mark.asyncio
    async def test_concurrent_analyses(self, sample_screenshot_data):
        """Test concurrent analysis performance."""
        reasoner = ReasoningAI()
        tasks = generate_tasks(10)
        
        # Run analyses concurrently
        start_time = asyncio.get_event_loop().time()
        results = await asyncio.gather(*[
            reasoner.analyze_screenshot(sample_screenshot_data, task)
            for task in tasks
        ])
        end_time = asyncio.get_event_loop().time()
        
        # Should complete all 10 in roughly the time of one (due to async)
        elapsed = end_time - start_time
        assert elapsed < 1.0  # Should be ~0.5s for mock delay
        assert len(results) == 10
        assert all(isinstance(r, AIAnalysisResult) for r in results)
    
    @pytest.mark.asyncio
    @patch('openai.OpenAI')
    async def test_openai_retry_performance(self, mock_openai_class, temp_dir, sample_tasks):
        """Test performance when OpenAI fails and falls back to mock."""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        mock_client.chat.completions.create.side_effect = Exception("API error")
        
        # Create test screenshot
        screenshot_path = temp_dir / "test.png"
        screenshot_path.write_bytes(b"PNG")
        
        screenshot_data: ScreenshotData = {
            "path": str(screenshot_path),
            "timestamp": datetime.now().isoformat(),
            "screen_id": 1
        }
        
        config = ReasoningConfig(mock_mode=False, openai_api_key="test-key")
        reasoner = ReasoningAI(config=config)
        
        # Should fallback quickly
        start_time = asyncio.get_event_loop().time()
        result = await reasoner.analyze_screenshot(screenshot_data, sample_tasks[0])
        end_time = asyncio.get_event_loop().time()
        
        elapsed = end_time - start_time
        assert elapsed < 1.0  # Fallback should be quick
        assert isinstance(result, AIAnalysisResult)


# ─────────────────────────────────────────────────────────────────────────────
# Main execution
# ─────────────────────────────────────────────────────────────────────────────


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
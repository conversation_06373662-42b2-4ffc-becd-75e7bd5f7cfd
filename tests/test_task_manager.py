"""
Comprehensive tests for the Task Manager module.

Tests cover:
- Task CRUD operations
- Task persistence to JSON
- Priority management and sorting
- Status transitions
- Error handling
- Edge cases
"""

import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import List
from unittest.mock import Mock, patch

import pytest

from src.interfaces import TaskD<PERSON>, Task<PERSON><PERSON>rity, TaskStatus, TaskNotFoundError
from src.task_manager.config import TaskManagerConfig
from src.task_manager.manager import TaskManager
from tests.test_utils import (
    assert_task_equal,
    generate_tasks,
    create_mock_config,
)


class TestTaskManager:
    """Test suite for TaskManager class."""
    
    # ─────────────────────────────────────────────────────────────────────────
    # Initialization Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_init_default_config(self, temp_dir):
        """Test initialization with default configuration."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        assert manager.config is not None
        assert isinstance(manager.config, TaskManagerConfig)
        assert manager.config.tasks_file == temp_dir / "tasks.json"
        assert manager._tasks == []
    
    def test_init_custom_config(self, temp_dir):
        """Test initialization with custom configuration."""
        config = TaskManagerConfig(
            tasks_file=temp_dir / "custom_tasks.json",
            auto_save=False
        )
        manager = TaskManager(config=config)
        
        assert manager.config == config
        assert manager.config.tasks_file == temp_dir / "custom_tasks.json"
        assert manager.config.auto_save is False
    
    def test_init_creates_directory(self, temp_dir):
        """Test that initialization creates necessary directories."""
        tasks_dir = temp_dir / "nested" / "path"
        manager = TaskManager(storage_path=tasks_dir / "tasks.json")
        
        assert tasks_dir.exists()
        assert tasks_dir.is_dir()
    
    def test_init_loads_existing_tasks(self, temp_dir, sample_tasks):
        """Test loading existing tasks from file on initialization."""
        tasks_file = temp_dir / "tasks.json"
        
        # Pre-populate tasks file
        tasks_data = {
            "tasks": [
                {
                    "id": task.id,
                    "priority": task.priority.value,
                    "description": task.description,
                    "status": task.status.value,
                    "created_at": task.created_at.isoformat(),
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None
                }
                for task in sample_tasks
            ],
            "last_updated": datetime.now().isoformat()
        }
        
        with open(tasks_file, 'w') as f:
            json.dump(tasks_data, f)
        
        # Initialize manager
        manager = TaskManager(storage_path=tasks_file)
        
        # Verify tasks were loaded
        assert len(manager._tasks) == len(sample_tasks)
        loaded_tasks = manager.get_all_tasks()
        
        for original, loaded in zip(sample_tasks, loaded_tasks):
            assert_task_equal(original, loaded)
    
    # ─────────────────────────────────────────────────────────────────────────
    # Task Creation Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_add_task_basic(self, temp_dir):
        """Test adding a basic task."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        task = manager.add_task("Test task", TaskPriority.P1)
        
        assert task.id.startswith("task-")
        assert task.description == "Test task"
        assert task.priority == TaskPriority.P1
        assert task.status == TaskStatus.PENDING
        assert task.created_at is not None
        assert task.completed_at is None
        assert len(manager._tasks) == 1
    
    def test_add_task_strips_whitespace(self, temp_dir):
        """Test that task descriptions are stripped of whitespace."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        task = manager.add_task("  Test task with spaces  ", TaskPriority.P2)
        
        assert task.description == "Test task with spaces"
    
    def test_add_task_auto_save(self, temp_dir):
        """Test that tasks are auto-saved when configured."""
        tasks_file = temp_dir / "tasks.json"
        config = TaskManagerConfig(tasks_file=tasks_file, auto_save=True)
        manager = TaskManager(config=config)
        
        manager.add_task("Auto-save test", TaskPriority.P1)
        
        # Verify file was created
        assert tasks_file.exists()
        
        # Verify content
        with open(tasks_file, 'r') as f:
            data = json.load(f)
        
        assert len(data["tasks"]) == 1
        assert data["tasks"][0]["description"] == "Auto-save test"
    
    def test_add_task_no_auto_save(self, temp_dir):
        """Test that tasks are not auto-saved when disabled."""
        tasks_file = temp_dir / "tasks.json"
        config = TaskManagerConfig(tasks_file=tasks_file, auto_save=False)
        manager = TaskManager(config=config)
        
        manager.add_task("No auto-save test", TaskPriority.P1)
        
        # File should not exist
        assert not tasks_file.exists()
    
    def test_add_multiple_tasks_unique_ids(self, temp_dir):
        """Test that multiple tasks get unique IDs."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        tasks = [
            manager.add_task(f"Task {i}", TaskPriority.P1)
            for i in range(10)
        ]
        
        ids = [task.id for task in tasks]
        assert len(set(ids)) == 10  # All IDs are unique
    
    # ─────────────────────────────────────────────────────────────────────────
    # Task Retrieval Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_get_current_task_empty(self, temp_dir):
        """Test getting current task when no tasks exist."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        current = manager.get_current_task()
        assert current is None
    
    def test_get_current_task_single_pending(self, temp_dir):
        """Test getting current task with single pending task."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        task = manager.add_task("Single task", TaskPriority.P1)
        
        current = manager.get_current_task()
        assert current == task
    
    def test_get_current_task_priority_order(self, temp_dir):
        """Test that current task respects priority order."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        # Add tasks in reverse priority order
        p5_task = manager.add_task("Low priority", TaskPriority.P5)
        p3_task = manager.add_task("Medium priority", TaskPriority.P3)
        p1_task = manager.add_task("High priority", TaskPriority.P1)
        
        current = manager.get_current_task()
        assert current == p1_task
    
    def test_get_current_task_in_progress_takes_precedence(self, temp_dir):
        """Test that in-progress task takes precedence over higher priority pending."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        p1_task = manager.add_task("High priority", TaskPriority.P1)
        p3_task = manager.add_task("Medium priority", TaskPriority.P3)
        
        # Mark P3 as in progress
        manager.update_task_status(p3_task.id, TaskStatus.IN_PROGRESS)
        
        current = manager.get_current_task()
        assert current.id == p3_task.id
        assert current.status == TaskStatus.IN_PROGRESS
    
    def test_get_current_task_skips_completed(self, temp_dir):
        """Test that completed tasks are not returned as current."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        completed_task = manager.add_task("Completed", TaskPriority.P1)
        manager.mark_complete(completed_task.id)
        
        pending_task = manager.add_task("Pending", TaskPriority.P2)
        
        current = manager.get_current_task()
        assert current == pending_task
    
    def test_get_current_task_skips_blocked(self, temp_dir):
        """Test that blocked tasks are not returned as current."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        blocked_task = manager.add_task("Blocked", TaskPriority.P1)
        manager.update_task_status(blocked_task.id, TaskStatus.BLOCKED)
        
        pending_task = manager.add_task("Pending", TaskPriority.P2)
        
        current = manager.get_current_task()
        assert current == pending_task
    
    def test_get_all_tasks_empty(self, temp_dir):
        """Test getting all tasks when none exist."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        tasks = manager.get_all_tasks()
        assert tasks == []
    
    def test_get_all_tasks_sorted_by_priority(self, temp_dir):
        """Test that all tasks are returned sorted by priority."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        # Add tasks in random order
        manager.add_task("P3 task", TaskPriority.P3)
        manager.add_task("P1 task", TaskPriority.P1)
        manager.add_task("P5 task", TaskPriority.P5)
        manager.add_task("P2 task", TaskPriority.P2)
        manager.add_task("P4 task", TaskPriority.P4)
        
        tasks = manager.get_all_tasks()
        
        # Verify sorted by priority
        priorities = [task.priority.value for task in tasks]
        assert priorities == [1, 2, 3, 4, 5]
    
    # ─────────────────────────────────────────────────────────────────────────
    # Task Update Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_update_task_status_valid(self, temp_dir):
        """Test updating task status with valid status."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        task = manager.add_task("Test task", TaskPriority.P1)
        
        updated = manager.update_task_status(task.id, TaskStatus.IN_PROGRESS)
        
        assert updated.status == TaskStatus.IN_PROGRESS
        assert updated.id == task.id
        assert manager._get_task_by_id(task.id).status == TaskStatus.IN_PROGRESS
    
    def test_update_task_status_sets_completed_at(self, temp_dir):
        """Test that completed_at is set when marking as completed."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        task = manager.add_task("Test task", TaskPriority.P1)
        
        assert task.completed_at is None
        
        updated = manager.update_task_status(task.id, TaskStatus.COMPLETED)
        
        assert updated.completed_at is not None
        assert isinstance(updated.completed_at, datetime)
    
    def test_update_task_status_invalid_id(self, temp_dir):
        """Test updating status with invalid task ID."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        with pytest.raises(TaskNotFoundError) as exc_info:
            manager.update_task_status("invalid-id", TaskStatus.COMPLETED)
        
        assert "Task invalid-id not found" in str(exc_info.value)
    
    def test_update_task_order(self, temp_dir):
        """Test updating task priority/order."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        task = manager.add_task("Test task", TaskPriority.P5)
        
        updated = manager.update_task_order(task.id, TaskPriority.P1)
        
        assert updated.priority == TaskPriority.P1
        assert manager._get_task_by_id(task.id).priority == TaskPriority.P1
    
    def test_update_task_order_invalid_id(self, temp_dir):
        """Test updating order with invalid task ID."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        with pytest.raises(TaskNotFoundError):
            manager.update_task_order("invalid-id", TaskPriority.P1)
    
    def test_mark_complete(self, temp_dir):
        """Test marking a task as complete."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        task = manager.add_task("Test task", TaskPriority.P1)
        
        completed = manager.mark_complete(task.id)
        
        assert completed.status == TaskStatus.COMPLETED
        assert completed.completed_at is not None
    
    def test_mark_complete_invalid_id(self, temp_dir):
        """Test marking invalid task as complete."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        with pytest.raises(TaskNotFoundError):
            manager.mark_complete("invalid-id")
    
    # ─────────────────────────────────────────────────────────────────────────
    # Task Import Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_import_tasks_basic(self, temp_dir):
        """Test importing tasks from string list."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        task_list = [
            "First task",
            "Second task",
            "Third task"
        ]
        
        imported = manager.import_tasks(task_list)
        
        assert len(imported) == 3
        assert imported[0].description == "First task"
        assert imported[0].priority == TaskPriority.P1
        assert imported[1].priority == TaskPriority.P2
        assert imported[2].priority == TaskPriority.P3
    
    def test_import_tasks_clears_existing(self, temp_dir):
        """Test that import clears existing tasks."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        # Add existing task
        manager.add_task("Existing task", TaskPriority.P1)
        assert len(manager._tasks) == 1
        
        # Import new tasks
        manager.import_tasks(["New task 1", "New task 2"])
        
        assert len(manager._tasks) == 2
        assert all("New task" in task.description for task in manager._tasks)
    
    def test_import_tasks_priority_capping(self, temp_dir):
        """Test that priorities are capped at P5."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        # Import more than 5 tasks
        task_list = [f"Task {i}" for i in range(10)]
        imported = manager.import_tasks(task_list)
        
        # First 5 should have P1-P5
        for i in range(5):
            assert imported[i].priority == TaskPriority(i + 1)
        
        # Remaining should all be P5
        for i in range(5, 10):
            assert imported[i].priority == TaskPriority.P5
    
    def test_import_empty_list(self, temp_dir):
        """Test importing empty task list."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        manager.add_task("Existing", TaskPriority.P1)
        
        imported = manager.import_tasks([])
        
        assert imported == []
        assert len(manager._tasks) == 0
    
    # ─────────────────────────────────────────────────────────────────────────
    # Persistence Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_save_tasks_format(self, temp_dir):
        """Test the format of saved tasks file."""
        tasks_file = temp_dir / "tasks.json"
        manager = TaskManager(storage_path=tasks_file)
        
        # Add some tasks
        task1 = manager.add_task("Task 1", TaskPriority.P1)
        task2 = manager.add_task("Task 2", TaskPriority.P2)
        manager.mark_complete(task2.id)
        
        # Force save
        manager._save_tasks()
        
        # Verify file content
        with open(tasks_file, 'r') as f:
            data = json.load(f)
        
        assert "tasks" in data
        assert "last_updated" in data
        assert len(data["tasks"]) == 2
        
        # Check task format
        saved_task = data["tasks"][0]
        assert all(key in saved_task for key in [
            "id", "priority", "description", "status", "created_at", "completed_at"
        ])
    
    def test_load_tasks_corrupt_file(self, temp_dir, caplog):
        """Test loading from corrupt tasks file."""
        tasks_file = temp_dir / "tasks.json"
        
        # Create corrupt file
        with open(tasks_file, 'w') as f:
            f.write("{ invalid json")
        
        # Should not raise, but log error
        manager = TaskManager(storage_path=tasks_file)
        
        assert len(manager._tasks) == 0
        assert "Failed to load tasks" in caplog.text
    
    def test_save_tasks_error_handling(self, temp_dir, caplog):
        """Test error handling during save."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        manager.add_task("Test", TaskPriority.P1)
        
        # Make directory read-only to cause save error
        with patch("builtins.open", side_effect=PermissionError("No write access")):
            manager._save_tasks()
        
        assert "Failed to save tasks" in caplog.text
    
    # ─────────────────────────────────────────────────────────────────────────
    # Edge Cases and Error Handling
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_concurrent_status_updates(self, temp_dir):
        """Test multiple status updates in succession."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        task = manager.add_task("Test", TaskPriority.P1)
        
        # Update status multiple times
        manager.update_task_status(task.id, TaskStatus.IN_PROGRESS)
        manager.update_task_status(task.id, TaskStatus.BLOCKED)
        manager.update_task_status(task.id, TaskStatus.IN_PROGRESS)
        completed = manager.update_task_status(task.id, TaskStatus.COMPLETED)
        
        assert completed.status == TaskStatus.COMPLETED
        assert completed.completed_at is not None
    
    def test_completed_at_not_overwritten(self, temp_dir):
        """Test that completed_at timestamp is not overwritten."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        task = manager.add_task("Test", TaskPriority.P1)
        
        # Mark complete
        completed1 = manager.mark_complete(task.id)
        original_time = completed1.completed_at
        
        # Update to different status and back to completed
        manager.update_task_status(task.id, TaskStatus.IN_PROGRESS)
        completed2 = manager.update_task_status(task.id, TaskStatus.COMPLETED)
        
        # Original completed_at should be preserved
        assert completed2.completed_at == original_time
    
    def test_very_long_description(self, temp_dir):
        """Test handling of very long task descriptions."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        long_desc = "A" * 10000  # 10k character description
        task = manager.add_task(long_desc, TaskPriority.P1)
        
        assert task.description == long_desc
        assert len(manager._tasks) == 1
    
    def test_special_characters_in_description(self, temp_dir):
        """Test handling of special characters in descriptions."""
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        special_desc = 'Test with "quotes" and \n newlines and emoji 🚀'
        task = manager.add_task(special_desc, TaskPriority.P1)
        
        assert task.description == special_desc
        
        # Save and reload
        manager._save_tasks()
        manager2 = TaskManager(storage_path=temp_dir / "tasks.json")
        
        loaded_task = manager2.get_all_tasks()[0]
        assert loaded_task.description == special_desc


# ─────────────────────────────────────────────────────────────────────────────
# Integration Tests
# ─────────────────────────────────────────────────────────────────────────────


class TestTaskManagerIntegration:
    """Integration tests for TaskManager with other components."""
    
    def test_full_task_lifecycle(self, temp_dir):
        """Test complete task lifecycle from creation to completion."""
        tasks_file = temp_dir / "tasks.json"
        manager = TaskManager(storage_path=tasks_file)
        
        # Create tasks
        task1 = manager.add_task("Research feature", TaskPriority.P2)
        task2 = manager.add_task("Implement feature", TaskPriority.P1)
        task3 = manager.add_task("Write tests", TaskPriority.P3)
        
        # Get current task (should be P1)
        current = manager.get_current_task()
        assert current.id == task2.id
        
        # Start working on it
        manager.update_task_status(current.id, TaskStatus.IN_PROGRESS)
        
        # Complete it
        manager.mark_complete(current.id)
        
        # Next current should be P2
        current = manager.get_current_task()
        assert current.id == task1.id
        
        # Reload from disk and verify state
        manager2 = TaskManager(storage_path=tasks_file)
        tasks = manager2.get_all_tasks()
        
        assert len(tasks) == 3
        assert sum(1 for t in tasks if t.status == TaskStatus.COMPLETED) == 1
        assert sum(1 for t in tasks if t.status == TaskStatus.PENDING) == 2
    
    def test_persistence_across_instances(self, temp_dir):
        """Test that tasks persist across manager instances."""
        tasks_file = temp_dir / "tasks.json"
        
        # First instance - create tasks
        manager1 = TaskManager(storage_path=tasks_file)
        ids = []
        for i in range(5):
            task = manager1.add_task(f"Task {i+1}", TaskPriority((i % 5) + 1))
            ids.append(task.id)
        
        # Mark some as complete
        manager1.mark_complete(ids[0])
        manager1.update_task_status(ids[1], TaskStatus.IN_PROGRESS)
        
        # Second instance - load and verify
        manager2 = TaskManager(storage_path=tasks_file)
        tasks = manager2.get_all_tasks()
        
        assert len(tasks) == 5
        
        # Verify statuses persisted
        task_map = {t.id: t for t in tasks}
        assert task_map[ids[0]].status == TaskStatus.COMPLETED
        assert task_map[ids[1]].status == TaskStatus.IN_PROGRESS
        assert all(task_map[ids[i]].status == TaskStatus.PENDING for i in range(2, 5))
    
    @pytest.mark.parametrize("task_count", [10, 50, 100])
    def test_performance_with_many_tasks(self, temp_dir, task_count):
        """Test performance with varying numbers of tasks."""
        from tests.test_utils import PerformanceTimer
        
        manager = TaskManager(storage_path=temp_dir / "tasks.json")
        
        # Time task creation
        with PerformanceTimer(f"Creating {task_count} tasks") as timer:
            for i in range(task_count):
                manager.add_task(f"Task {i+1}", TaskPriority((i % 5) + 1))
        timer.assert_faster_than(1.0)  # Should create 100 tasks in < 1 second
        
        # Time getting current task
        with PerformanceTimer("Getting current task") as timer:
            current = manager.get_current_task()
        timer.assert_faster_than(0.01)  # Should be very fast
        
        # Time getting all tasks (sorted)
        with PerformanceTimer("Getting all tasks sorted") as timer:
            all_tasks = manager.get_all_tasks()
        timer.assert_faster_than(0.1)  # Sorting should still be fast


# ─────────────────────────────────────────────────────────────────────────────
# Main execution
# ─────────────────────────────────────────────────────────────────────────────


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
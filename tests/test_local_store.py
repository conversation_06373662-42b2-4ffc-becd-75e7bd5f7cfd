"""
Comprehensive tests for the Local Store module.

Tests cover:
- Screenshot metadata storage
- Task list persistence
- Analysis and justification logging
- Activity summaries
- Data compression
- Cleanup operations
- Error handling
"""

import gzip
import json
import shutil
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import List
from unittest.mock import Mock, patch, mock_open

import pytest

from src.interfaces import (
    AIAnalysisResult,
    JustificationResponse,
    ScreenshotData,
    TaskData,
    TaskPriority,
    TaskStatus,
)
from src.local_store.config import LocalStoreConfig
from src.local_store.store import LocalStore
from tests.test_utils import (
    create_date_folders,
    create_mock_screenshot_files,
    generate_analysis_results,
    generate_tasks,
    read_jsonl,
    write_jsonl,
)


class TestLocalStore:
    """Test suite for LocalStore class."""
    
    # ─────────────────────────────────────────────────────────────────────────
    # Initialization Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_init_default_config(self, temp_dir):
        """Test initialization with default configuration."""
        store = LocalStore(base_dir=temp_dir)
        
        assert store.config is not None
        assert isinstance(store.config, LocalStoreConfig)
        assert store.config.base_path == temp_dir
        assert (temp_dir / "screenshots").exists()
        assert (temp_dir / "logs").exists()
    
    def test_init_custom_config(self, temp_dir):
        """Test initialization with custom configuration."""
        config = LocalStoreConfig(
            base_path=temp_dir,
            screenshots_path=temp_dir / "custom_screenshots",
            logs_path=temp_dir / "custom_logs",
            compression_enabled=False,
            max_storage_days=30
        )
        store = LocalStore(config=config)
        
        assert store.config == config
        assert (temp_dir / "custom_screenshots").exists()
        assert (temp_dir / "custom_logs").exists()
    
    def test_init_creates_directories(self, temp_dir):
        """Test that initialization creates all required directories."""
        nested_path = temp_dir / "nested" / "storage"
        store = LocalStore(base_dir=nested_path)
        
        assert nested_path.exists()
        assert (nested_path / "screenshots").exists()
        assert (nested_path / "logs").exists()
    
    # ─────────────────────────────────────────────────────────────────────────
    # Screenshot Metadata Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_save_screenshot_metadata_basic(self, temp_dir, sample_screenshot_data):
        """Test saving basic screenshot metadata."""
        store = LocalStore(base_dir=temp_dir)
        
        # Create dummy screenshot file
        screenshot_path = Path(sample_screenshot_data["path"])
        screenshot_path.parent.mkdir(parents=True, exist_ok=True)
        screenshot_path.write_bytes(b"PNG_DATA")
        
        # Save metadata
        store.save_screenshot_metadata(sample_screenshot_data)
        
        # Verify metadata file created
        metadata_path = screenshot_path.with_suffix(".json")
        assert metadata_path.exists()
        
        # Verify content
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)
        
        assert metadata["timestamp"] == sample_screenshot_data["timestamp"]
        assert metadata["screen_id"] == sample_screenshot_data["screen_id"]
        assert metadata["file_size"] == 8  # len(b"PNG_DATA")
        assert "saved_at" in metadata
    
    def test_save_screenshot_metadata_missing_file(self, temp_dir):
        """Test saving metadata for non-existent screenshot."""
        store = LocalStore(base_dir=temp_dir)
        
        screenshot_data: ScreenshotData = {
            "path": str(temp_dir / "missing.png"),
            "timestamp": datetime.now().isoformat(),
            "screen_id": 1
        }
        
        # Should still save metadata
        store.save_screenshot_metadata(screenshot_data)
        
        metadata_path = Path(screenshot_data["path"]).with_suffix(".json")
        assert metadata_path.exists()
        
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)
        assert metadata["file_size"] == 0
    
    def test_save_screenshot_metadata_error_handling(self, temp_dir, caplog):
        """Test error handling in screenshot metadata saving."""
        store = LocalStore(base_dir=temp_dir)
        
        # Invalid path
        screenshot_data: ScreenshotData = {
            "path": "/invalid\0path/screenshot.png",
            "timestamp": datetime.now().isoformat(),
            "screen_id": 1
        }
        
        with pytest.raises(Exception):
            store.save_screenshot_metadata(screenshot_data)
        
        assert "Failed to save screenshot metadata" in caplog.text
    
    # ─────────────────────────────────────────────────────────────────────────
    # Task Persistence Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_save_task_list_basic(self, temp_dir, sample_tasks):
        """Test saving task list."""
        store = LocalStore(base_dir=temp_dir)
        
        store.save_task_list(sample_tasks)
        
        # Verify file created
        tasks_file = temp_dir / "tasks.json"
        assert tasks_file.exists()
        
        # Verify content
        with open(tasks_file, 'r') as f:
            data = json.load(f)
        
        assert "last_saved" in data
        assert len(data["tasks"]) == len(sample_tasks)
        
        # Verify task data
        for saved, original in zip(data["tasks"], sample_tasks):
            assert saved["id"] == original.id
            assert saved["priority"] == original.priority.value
            assert saved["description"] == original.description
            assert saved["status"] == original.status.value
    
    def test_save_empty_task_list(self, temp_dir):
        """Test saving empty task list."""
        store = LocalStore(base_dir=temp_dir)
        
        store.save_task_list([])
        
        tasks_file = temp_dir / "tasks.json"
        assert tasks_file.exists()
        
        with open(tasks_file, 'r') as f:
            data = json.load(f)
        assert data["tasks"] == []
    
    def test_load_task_list_basic(self, temp_dir, sample_tasks):
        """Test loading task list."""
        store = LocalStore(base_dir=temp_dir)
        
        # Save tasks first
        store.save_task_list(sample_tasks)
        
        # Load them back
        loaded_tasks = store.load_task_list()
        
        assert len(loaded_tasks) == len(sample_tasks)
        
        for loaded, original in zip(loaded_tasks, sample_tasks):
            assert loaded.id == original.id
            assert loaded.priority == original.priority
            assert loaded.description == original.description
            assert loaded.status == original.status
    
    def test_load_task_list_no_file(self, temp_dir):
        """Test loading when no task file exists."""
        store = LocalStore(base_dir=temp_dir)
        
        tasks = store.load_task_list()
        assert tasks == []
    
    def test_load_task_list_corrupt_file(self, temp_dir, caplog):
        """Test loading from corrupt task file."""
        store = LocalStore(base_dir=temp_dir)
        
        # Create corrupt file
        tasks_file = temp_dir / "tasks.json"
        tasks_file.write_text("{ invalid json")
        
        tasks = store.load_task_list()
        
        assert tasks == []
        assert "Failed to load task list" in caplog.text
    
    # ─────────────────────────────────────────────────────────────────────────
    # Analysis Logging Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_save_analysis_log_basic(self, temp_dir, sample_analysis_result):
        """Test saving analysis log entry."""
        store = LocalStore(base_dir=temp_dir)
        
        store.save_analysis_log(sample_analysis_result)
        
        # Verify log file created in today's folder
        today = datetime.now().strftime("%Y%b%d").upper()
        log_file = temp_dir / "logs" / today / "analysis_log.jsonl"
        assert log_file.exists()
        
        # Verify content
        entries = read_jsonl(log_file)
        assert len(entries) == 1
        
        entry = entries[0]
        assert entry["on_task"] == sample_analysis_result.on_task
        assert entry["confidence"] == sample_analysis_result.confidence
        assert entry["reason"] == sample_analysis_result.reason
        assert entry["screenshot_ref"] == sample_analysis_result.screenshot_ref
        assert "timestamp" in entry
    
    def test_save_analysis_log_with_justification(
        self, temp_dir, sample_analysis_result, sample_justification_response
    ):
        """Test saving analysis log with justification."""
        store = LocalStore(base_dir=temp_dir)
        
        store.save_analysis_log(sample_analysis_result, sample_justification_response)
        
        # Verify content includes justification
        today = datetime.now().strftime("%Y%b%d").upper()
        log_file = temp_dir / "logs" / today / "analysis_log.jsonl"
        
        entries = read_jsonl(log_file)
        entry = entries[0]
        
        assert "justification" in entry
        assert entry["justification"]["user_input"] == sample_justification_response.user_input
        assert entry["justification"]["valid"] == sample_justification_response.valid
        assert "timestamp" in entry["justification"]
    
    def test_save_analysis_log_append(self, temp_dir):
        """Test that analysis logs are appended, not overwritten."""
        store = LocalStore(base_dir=temp_dir)
        
        # Save multiple entries
        for i in range(3):
            result = AIAnalysisResult(
                on_task=i % 2 == 0,
                confidence=0.8 + i * 0.05,
                reason=f"Reason {i+1}",
                screenshot_ref=f"/tmp/screenshot_{i+1}.png"
            )
            store.save_analysis_log(result)
        
        # Verify all entries exist
        today = datetime.now().strftime("%Y%b%d").upper()
        log_file = temp_dir / "logs" / today / "analysis_log.jsonl"
        
        entries = read_jsonl(log_file)
        assert len(entries) == 3
        assert [e["reason"] for e in entries] == ["Reason 1", "Reason 2", "Reason 3"]
    
    def test_save_justification_log(self, temp_dir, sample_justification_response):
        """Test saving justification to dedicated log."""
        store = LocalStore(base_dir=temp_dir)
        
        store.save_justification_log(sample_justification_response)
        
        # Verify file created
        today = datetime.now().strftime("%Y%b%d").upper()
        log_file = temp_dir / "logs" / today / "justification_log.jsonl"
        assert log_file.exists()
        
        # Verify content
        entries = read_jsonl(log_file)
        assert len(entries) == 1
        
        entry = entries[0]
        assert entry["user_input"] == sample_justification_response.user_input
        assert entry["valid"] == sample_justification_response.valid
        assert "timestamp" in entry
    
    # ─────────────────────────────────────────────────────────────────────────
    # Activity Summary Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_get_activity_summary_empty(self, temp_dir):
        """Test activity summary with no data."""
        store = LocalStore(base_dir=temp_dir)
        
        start = datetime.now() - timedelta(days=1)
        end = datetime.now()
        
        summary = store.get_activity_summary(start, end)
        
        assert summary["total_analyses"] == 0
        assert summary["on_task_count"] == 0
        assert summary["off_task_count"] == 0
        assert summary["average_confidence"] == 0.0
        assert summary["justifications_provided"] == 0
        assert summary["justifications_accepted"] == 0
        assert summary["daily_breakdown"] == {}
    
    def test_get_activity_summary_single_day(self, temp_dir):
        """Test activity summary for single day."""
        store = LocalStore(base_dir=temp_dir)
        
        # Create analysis entries for today
        analyses = [
            {"timestamp": datetime.now().isoformat(), "on_task": True, "confidence": 0.9, 
             "reason": "Working", "screenshot_ref": "/tmp/1.png"},
            {"timestamp": datetime.now().isoformat(), "on_task": False, "confidence": 0.8,
             "reason": "Browsing", "screenshot_ref": "/tmp/2.png"},
            {"timestamp": datetime.now().isoformat(), "on_task": True, "confidence": 0.85,
             "reason": "Working", "screenshot_ref": "/tmp/3.png",
             "justification": {"user_input": "Research", "valid": True}},
        ]
        
        today = datetime.now().strftime("%Y%b%d").upper()
        log_file = temp_dir / "logs" / today / "analysis_log.jsonl"
        log_file.parent.mkdir(parents=True, exist_ok=True)
        write_jsonl(log_file, analyses)
        
        # Get summary
        summary = store.get_activity_summary(
            datetime.now() - timedelta(hours=1),
            datetime.now() + timedelta(hours=1)
        )
        
        assert summary["total_analyses"] == 3
        assert summary["on_task_count"] == 2
        assert summary["off_task_count"] == 1
        assert summary["average_confidence"] == pytest.approx(0.85, 0.01)
        assert summary["justifications_provided"] == 1
        assert summary["justifications_accepted"] == 1
        assert today in summary["daily_breakdown"]
    
    def test_get_activity_summary_multi_day(self, temp_dir):
        """Test activity summary across multiple days."""
        store = LocalStore(base_dir=temp_dir)
        
        # Create entries for multiple days
        for days_ago in range(3):
            date = datetime.now() - timedelta(days=days_ago)
            date_str = date.strftime("%Y%b%d").upper()
            log_dir = temp_dir / "logs" / date_str
            log_dir.mkdir(parents=True, exist_ok=True)
            
            analyses = []
            for i in range(5):
                analyses.append({
                    "timestamp": (date + timedelta(hours=i)).isoformat(),
                    "on_task": i % 2 == 0,
                    "confidence": 0.7 + i * 0.05,
                    "reason": f"Activity {i}",
                    "screenshot_ref": f"/tmp/{date_str}_{i}.png"
                })
            
            write_jsonl(log_dir / "analysis_log.jsonl", analyses)
        
        # Get summary for all days
        summary = store.get_activity_summary(
            datetime.now() - timedelta(days=3),
            datetime.now() + timedelta(days=1)
        )
        
        assert summary["total_analyses"] == 15  # 3 days * 5 entries
        assert summary["on_task_count"] == 9   # 3 per day
        assert summary["off_task_count"] == 6  # 2 per day
        assert len(summary["daily_breakdown"]) == 3
    
    # ─────────────────────────────────────────────────────────────────────────
    # List Items Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_list_stored_items_screenshots(self, temp_dir):
        """Test listing screenshot items."""
        store = LocalStore(base_dir=temp_dir)
        
        # Create some screenshots
        today = datetime.now().strftime("%Y%b%d").upper()
        screenshot_dir = temp_dir / "screenshots" / today
        screenshot_dir.mkdir(parents=True, exist_ok=True)
        
        files = create_mock_screenshot_files(screenshot_dir, count=5)
        
        # List screenshots
        items = store.list_stored_items("screenshots")
        
        assert len(items) == 5
        assert all(item.suffix == ".png" for item in items)
        assert all(item.parent.name == today for item in items)
    
    def test_list_stored_items_logs(self, temp_dir):
        """Test listing log items."""
        store = LocalStore(base_dir=temp_dir)
        
        # Create log files
        today = datetime.now().strftime("%Y%b%d").upper()
        log_dir = temp_dir / "logs" / today
        log_dir.mkdir(parents=True, exist_ok=True)
        
        (log_dir / "analysis_log.jsonl").write_text("{}")
        (log_dir / "justification_log.jsonl").write_text("{}")
        
        # List logs
        items = store.list_stored_items("logs")
        
        assert len(items) == 2
        assert all(item.suffix == ".jsonl" for item in items)
    
    def test_list_stored_items_tasks(self, temp_dir):
        """Test listing task items."""
        store = LocalStore(base_dir=temp_dir)
        
        # Create tasks file
        (temp_dir / "tasks.json").write_text('{"tasks": []}')
        
        # List tasks
        items = store.list_stored_items("tasks")
        
        assert len(items) == 1
        assert items[0].name == "tasks.json"
    
    def test_list_stored_items_with_date(self, temp_dir):
        """Test listing items for specific date."""
        store = LocalStore(base_dir=temp_dir)
        
        # Create files for different dates
        yesterday = datetime.now() - timedelta(days=1)
        yesterday_str = yesterday.strftime("%Y%b%d").upper()
        today_str = datetime.now().strftime("%Y%b%d").upper()
        
        for date_str in [yesterday_str, today_str]:
            screenshot_dir = temp_dir / "screenshots" / date_str
            screenshot_dir.mkdir(parents=True, exist_ok=True)
            create_mock_screenshot_files(screenshot_dir, count=3)
        
        # List yesterday's items
        items = store.list_stored_items("screenshots", date=yesterday)
        
        assert len(items) == 3
        assert all(yesterday_str in str(item) for item in items)
    
    # ─────────────────────────────────────────────────────────────────────────
    # Compression Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_compress_old_screenshots_basic(self, temp_dir):
        """Test basic screenshot compression."""
        config = LocalStoreConfig(
            base_path=temp_dir,
            compression_enabled=True
        )
        store = LocalStore(config=config)
        
        # Create old screenshots
        old_date = datetime.now() - timedelta(days=10)
        old_date_str = old_date.strftime("%Y%b%d").upper()
        old_dir = temp_dir / "screenshots" / old_date_str
        old_dir.mkdir(parents=True, exist_ok=True)
        
        # Create PNG files
        png_files = []
        for i in range(3):
            png_file = old_dir / f"screenshot_{i}.png"
            png_file.write_bytes(b"PNG_DATA_" + str(i).encode())
            png_files.append(png_file)
        
        # Compress
        compressed_count = store.compress_old_screenshots(days_old=7)
        
        assert compressed_count == 3
        
        # Verify original files removed and compressed files exist
        for png_file in png_files:
            assert not png_file.exists()
            compressed_file = png_file.with_suffix(".png.gz")
            assert compressed_file.exists()
            
            # Verify content
            with gzip.open(compressed_file, 'rb') as f:
                content = f.read()
                assert content.startswith(b"PNG_DATA_")
    
    def test_compress_old_screenshots_disabled(self, temp_dir):
        """Test compression when disabled in config."""
        config = LocalStoreConfig(
            base_path=temp_dir,
            compression_enabled=False
        )
        store = LocalStore(config=config)
        
        # Create old screenshots
        old_date = datetime.now() - timedelta(days=10)
        old_date_str = old_date.strftime("%Y%b%d").upper()
        old_dir = temp_dir / "screenshots" / old_date_str
        old_dir.mkdir(parents=True, exist_ok=True)
        
        png_file = old_dir / "screenshot.png"
        png_file.write_bytes(b"PNG_DATA")
        
        # Try to compress
        compressed_count = store.compress_old_screenshots(days_old=7)
        
        assert compressed_count == 0
        assert png_file.exists()  # Original file still exists
    
    def test_compress_old_screenshots_already_compressed(self, temp_dir):
        """Test compression skips already compressed files."""
        config = LocalStoreConfig(
            base_path=temp_dir,
            compression_enabled=True
        )
        store = LocalStore(config=config)
        
        # Create old screenshot and pre-compress it
        old_date = datetime.now() - timedelta(days=10)
        old_date_str = old_date.strftime("%Y%b%d").upper()
        old_dir = temp_dir / "screenshots" / old_date_str
        old_dir.mkdir(parents=True, exist_ok=True)
        
        png_file = old_dir / "screenshot.png"
        png_file.write_bytes(b"PNG_DATA")
        
        compressed_file = png_file.with_suffix(".png.gz")
        with gzip.open(compressed_file, 'wb') as f:
            f.write(b"ALREADY_COMPRESSED")
        
        # Try to compress
        compressed_count = store.compress_old_screenshots(days_old=7)
        
        assert compressed_count == 0  # Nothing new compressed
        assert png_file.exists()  # Original still exists
        
        # Verify pre-existing compressed file unchanged
        with gzip.open(compressed_file, 'rb') as f:
            assert f.read() == b"ALREADY_COMPRESSED"
    
    # ─────────────────────────────────────────────────────────────────────────
    # Cleanup Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_cleanup_old_data_basic(self, temp_dir):
        """Test basic cleanup of old data."""
        store = LocalStore(base_dir=temp_dir)
        
        # Create folders for different dates
        folders = create_date_folders(temp_dir / "screenshots", days_back=10)
        create_date_folders(temp_dir / "logs", days_back=10)
        
        # Cleanup data older than 7 days
        removed_count = store.cleanup_old_data(days=7)
        
        # Should remove 3 days worth (days 7, 8, 9) from both screenshots and logs
        assert removed_count == 6
        
        # Verify old folders removed
        for i in range(7, 10):
            date = datetime.now() - timedelta(days=i)
            date_str = date.strftime("%Y%b%d").upper()
            assert not (temp_dir / "screenshots" / date_str).exists()
            assert not (temp_dir / "logs" / date_str).exists()
        
        # Verify recent folders still exist
        for i in range(7):
            date = datetime.now() - timedelta(days=i)
            date_str = date.strftime("%Y%b%d").upper()
            assert (temp_dir / "screenshots" / date_str).exists()
            assert (temp_dir / "logs" / date_str).exists()
    
    def test_cleanup_old_data_zero_days(self, temp_dir):
        """Test cleanup with zero days (disabled)."""
        store = LocalStore(base_dir=temp_dir)
        
        create_date_folders(temp_dir / "screenshots", days_back=5)
        
        removed_count = store.cleanup_old_data(days=0)
        
        assert removed_count == 0
        
        # All folders should still exist
        for i in range(5):
            date = datetime.now() - timedelta(days=i)
            date_str = date.strftime("%Y%b%d").upper()
            assert (temp_dir / "screenshots" / date_str).exists()
    
    def test_cleanup_old_data_invalid_folders(self, temp_dir):
        """Test cleanup handles invalid folder names gracefully."""
        store = LocalStore(base_dir=temp_dir)
        
        # Create some valid date folders
        create_date_folders(temp_dir / "screenshots", days_back=5)
        
        # Create invalid folders
        (temp_dir / "screenshots" / "invalid_name").mkdir()
        (temp_dir / "screenshots" / "123").mkdir()
        (temp_dir / "screenshots" / "test.txt").write_text("file, not folder")
        
        # Cleanup should not crash
        removed_count = store.cleanup_old_data(days=3)
        
        # Invalid folders should remain
        assert (temp_dir / "screenshots" / "invalid_name").exists()
        assert (temp_dir / "screenshots" / "123").exists()
        assert (temp_dir / "screenshots" / "test.txt").exists()
    
    # ─────────────────────────────────────────────────────────────────────────
    # Error Handling Tests
    # ─────────────────────────────────────────────────────────────────────────
    
    def test_save_analysis_log_error_handling(self, temp_dir, caplog):
        """Test error handling in analysis log saving."""
        store = LocalStore(base_dir=temp_dir)
        
        # Make logs directory read-only
        log_dir = temp_dir / "logs"
        
        with patch("builtins.open", side_effect=PermissionError("No write access")):
            with pytest.raises(PermissionError):
                store.save_analysis_log(AIAnalysisResult(
                    on_task=True,
                    confidence=0.9,
                    reason="Test",
                    screenshot_ref="/tmp/test.png"
                ))
        
        assert "Failed to save analysis log" in caplog.text
    
    def test_save_task_list_error_handling(self, temp_dir, caplog, sample_tasks):
        """Test error handling in task list saving."""
        store = LocalStore(base_dir=temp_dir)
        
        with patch("builtins.open", side_effect=IOError("Disk full")):
            with pytest.raises(IOError):
                store.save_task_list(sample_tasks)
        
        assert "Failed to save task list" in caplog.text
    
    def test_get_activity_summary_corrupt_log(self, temp_dir, caplog):
        """Test activity summary with corrupt log files."""
        store = LocalStore(base_dir=temp_dir)
        
        # Create corrupt log file
        today = datetime.now().strftime("%Y%b%d").upper()
        log_file = temp_dir / "logs" / today / "analysis_log.jsonl"
        log_file.parent.mkdir(parents=True, exist_ok=True)
        log_file.write_text('{"valid": "entry"}\n{invalid json}\n{"another": "valid"}')
        
        # Should handle gracefully
        summary = store.get_activity_summary(
            datetime.now() - timedelta(hours=1),
            datetime.now() + timedelta(hours=1)
        )
        
        # Should process valid entries
        assert summary["total_analyses"] >= 0
        assert "Error reading log file" in caplog.text


# ─────────────────────────────────────────────────────────────────────────────
# Integration Tests
# ─────────────────────────────────────────────────────────────────────────────


class TestLocalStoreIntegration:
    """Integration tests for LocalStore with full workflows."""
    
    def test_full_storage_workflow(self, temp_dir):
        """Test complete storage workflow."""
        store = LocalStore(base_dir=temp_dir)
        
        # 1. Save screenshot and metadata
        screenshot_path = temp_dir / "test_screenshot.png"
        screenshot_path.write_bytes(b"PNG_IMAGE_DATA")
        
        screenshot_data: ScreenshotData = {
            "path": str(screenshot_path),
            "timestamp": datetime.now().isoformat(),
            "screen_id": 1
        }
        store.save_screenshot_metadata(screenshot_data)
        
        # 2. Save analysis
        analysis = AIAnalysisResult(
            on_task=False,
            confidence=0.85,
            reason="User browsing social media",
            screenshot_ref=str(screenshot_path)
        )
        store.save_analysis_log(analysis)
        
        # 3. Save justification
        justification = JustificationResponse(
            user_input="Checking work-related posts",
            valid=True,
            timestamp=datetime.now()
        )
        store.save_analysis_log(analysis, justification)
        store.save_justification_log(justification)
        
        # 4. Save tasks
        tasks = generate_tasks(5)
        store.save_task_list(tasks)
        
        # 5. Get activity summary
        summary = store.get_activity_summary(
            datetime.now() - timedelta(hours=1),
            datetime.now() + timedelta(hours=1)
        )
        
        assert summary["total_analyses"] == 2
        assert summary["off_task_count"] == 2
        assert summary["justifications_provided"] == 1
        assert summary["justifications_accepted"] == 1
        
        # 6. List items
        screenshots = store.list_stored_items("screenshots")
        logs = store.list_stored_items("logs")
        task_files = store.list_stored_items("tasks")
        
        assert len(screenshots) >= 0  # May be in date folder
        assert len(logs) >= 2  # analysis and justification logs
        assert len(task_files) == 1
    
    def test_multi_day_storage_and_cleanup(self, temp_dir):
        """Test storage across multiple days with cleanup."""
        config = LocalStoreConfig(
            base_path=temp_dir,
            compression_enabled=True,
            max_storage_days=7
        )
        store = LocalStore(config=config)
        
        # Create data for multiple days
        for days_ago in range(10):
            date = datetime.now() - timedelta(days=days_ago)
            date_str = date.strftime("%Y%b%d").upper()
            
            # Create screenshots
            screenshot_dir = temp_dir / "screenshots" / date_str
            screenshot_dir.mkdir(parents=True, exist_ok=True)
            for i in range(3):
                (screenshot_dir / f"screenshot_{i}.png").write_bytes(b"PNG")
            
            # Create logs
            log_dir = temp_dir / "logs" / date_str
            log_dir.mkdir(parents=True, exist_ok=True)
            (log_dir / "analysis_log.jsonl").write_text("{}")
        
        # Compress old screenshots (older than 7 days)
        compressed = store.compress_old_screenshots(days_old=7)
        assert compressed == 9  # 3 days * 3 files
        
        # Cleanup very old data (older than 7 days)
        removed = store.cleanup_old_data(days=7)
        assert removed == 6  # 3 days * 2 folders (screenshots + logs)
        
        # Verify only recent 7 days remain
        remaining_screenshot_days = list((temp_dir / "screenshots").iterdir())
        remaining_log_days = list((temp_dir / "logs").iterdir())
        
        assert len(remaining_screenshot_days) == 7
        assert len(remaining_log_days) == 7
    
    @pytest.mark.parametrize("num_entries", [100, 500, 1000])
    def test_performance_with_many_entries(self, temp_dir, num_entries):
        """Test performance with many log entries."""
        from tests.test_utils import PerformanceTimer
        
        store = LocalStore(base_dir=temp_dir)
        
        # Create many analysis entries
        with PerformanceTimer(f"Creating {num_entries} analysis entries") as timer:
            for i in range(num_entries):
                result = AIAnalysisResult(
                    on_task=i % 3 != 0,
                    confidence=0.7 + (i % 30) * 0.01,
                    reason=f"Activity {i}",
                    screenshot_ref=f"/tmp/screenshot_{i}.png"
                )
                store.save_analysis_log(result)
        timer.assert_faster_than(5.0)  # Should handle 1000 entries in < 5 seconds
        
        # Test summary generation performance
        with PerformanceTimer("Generating activity summary") as timer:
            summary = store.get_activity_summary(
                datetime.now() - timedelta(hours=1),
                datetime.now() + timedelta(hours=1)
            )
        timer.assert_faster_than(1.0)  # Summary should be fast even with many entries
        
        assert summary["total_analyses"] == num_entries


# ─────────────────────────────────────────────────────────────────────────────
# Main execution
# ─────────────────────────────────────────────────────────────────────────────


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
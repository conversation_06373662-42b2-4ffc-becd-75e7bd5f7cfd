# Task Manager Module PRD

## Overview
The Task Manager module handles importing, tracking, and persisting task lists for the Productivity Guard system. It serves as the foundation for task-based productivity monitoring.

## Requirements Mapping

| PRD Requirement | Description | Implementation |
|----------------|-------------|----------------|
| **F-1** | Import daily task list (CLI or chat) and persist to local store | `TaskManager.import_tasks()` |
| **F-2** | Track current active task pointer (default: Priority #1) | `TaskManager.current_task` property |
| **F-3** | Allow user to explicitly update task order or mark complete | `TaskManager.update_task()`, `TaskManager.complete_task()` |
| **N-2** | Modular codebase with TypeHinted public interfaces | All methods with type hints, passes mypy --strict |
| **N-3** | Configurable via CFG dataclass | `TaskConfig` dataclass for all settings |

## Implementation Approach

```python
# ── /src/task_manager/config.py ─────────────────────────────────────────────────
from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Final

__all__: Final = ["TaskConfig"]

@dataclass(slots=True)
class TaskConfig:
    """
    Configuration for task management.
    
    Requirement: N-3
    Version: v01
    """
    tasks_file: Path = Path("./data/tasks.json")
    auto_save: bool = True
    default_priority: int = 1
```

```python
# ── /src/task_manager/models.py ─────────────────────────────────────────────────
from __future__ import annotations

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Optional

__all__ = ["Task", "TaskStatus", "TaskList"]

class TaskStatus(Enum):
    """Task completion status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    DEFERRED = "deferred"

@dataclass
class Task:
    """
    Individual task representation.
    
    Requirement: F-1, F-2
    Version: v01
    """
    id: str
    title: str
    priority: int
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    justifications: list[str] = field(default_factory=list)

@dataclass
class TaskList:
    """
    Collection of tasks with ordering.
    
    Requirement: F-1, F-2, F-3
    Version: v01
    """
    tasks: list[Task] = field(default_factory=list)
    current_task_id: Optional[str] = None
    last_updated: datetime = field(default_factory=datetime.now)
```

```python
# ── /src/task_manager/manager.py ────────────────────────────────────────────────
from __future__ import annotations

import json
import logging
import uuid
from datetime import datetime
from pathlib import Path
from typing import Optional

from .config import TaskConfig
from .models import Task, TaskList, TaskStatus

__all__ = ["TaskManager"]

_LOG = logging.getLogger(__name__)

class TaskManager:
    """
    Manages task list operations and persistence.
    
    Requirement: F-1, F-2, F-3, N-2
    Version: v01
    """
    
    def __init__(self, config: TaskConfig) -> None:
        """
        Initialize task manager with configuration.
        
        Requirement: N-2, N-3
        Version: v01
        """
        self.config = config
        self.task_list = TaskList()
        self._load_tasks()
    
    def import_tasks(self, task_descriptions: list[str]) -> None:
        """
        Import task list from descriptions.
        
        Requirement: F-1
        Version: v01
        """
        self.task_list.tasks.clear()
        
        for idx, description in enumerate(task_descriptions, 1):
            task = Task(
                id=str(uuid.uuid4()),
                title=description.strip(),
                priority=idx,
                status=TaskStatus.PENDING
            )
            self.task_list.tasks.append(task)
        
        # Set first task as current by default
        if self.task_list.tasks:
            self.task_list.current_task_id = self.task_list.tasks[0].id
        
        self.task_list.last_updated = datetime.now()
        
        if self.config.auto_save:
            self._save_tasks()
        
        _LOG.info(f"Imported {len(task_descriptions)} tasks")
    
    @property
    def current_task(self) -> Optional[Task]:
        """
        Get the current active task.
        
        Requirement: F-2
        Version: v01
        """
        if not self.task_list.current_task_id:
            return None
        
        for task in self.task_list.tasks:
            if task.id == self.task_list.current_task_id:
                return task
        
        return None
    
    def update_task_order(self, task_id: str, new_priority: int) -> bool:
        """
        Update task priority/order.
        
        Requirement: F-3
        Version: v01
        """
        task = self._get_task_by_id(task_id)
        if not task:
            _LOG.warning(f"Task {task_id} not found")
            return False
        
        # Reorder tasks
        old_priority = task.priority
        task.priority = new_priority
        
        # Adjust other task priorities
        for other_task in self.task_list.tasks:
            if other_task.id == task_id:
                continue
            
            if old_priority < new_priority:
                # Moving down: shift tasks up
                if old_priority < other_task.priority <= new_priority:
                    other_task.priority -= 1
            else:
                # Moving up: shift tasks down
                if new_priority <= other_task.priority < old_priority:
                    other_task.priority += 1
        
        # Sort tasks by priority
        self.task_list.tasks.sort(key=lambda t: t.priority)
        self.task_list.last_updated = datetime.now()
        
        if self.config.auto_save:
            self._save_tasks()
        
        _LOG.info(f"Updated task {task_id} priority to {new_priority}")
        return True
    
    def complete_task(self, task_id: str) -> bool:
        """
        Mark a task as completed.
        
        Requirement: F-3
        Version: v01
        """
        task = self._get_task_by_id(task_id)
        if not task:
            _LOG.warning(f"Task {task_id} not found")
            return False
        
        task.status = TaskStatus.COMPLETED
        task.completed_at = datetime.now()
        
        # Move to next pending task
        next_task = self._get_next_pending_task()
        if next_task:
            self.task_list.current_task_id = next_task.id
        else:
            self.task_list.current_task_id = None
        
        self.task_list.last_updated = datetime.now()
        
        if self.config.auto_save:
            self._save_tasks()
        
        _LOG.info(f"Completed task {task_id}")
        return True
    
    def switch_task(self, task_id: str, justification: str) -> bool:
        """
        Switch to a different task with justification.
        
        Requirement: F-2, F-8
        Version: v01
        """
        task = self._get_task_by_id(task_id)
        if not task:
            _LOG.warning(f"Task {task_id} not found")
            return False
        
        # Add justification to current task if switching away
        if self.current_task and self.current_task.id != task_id:
            self.current_task.justifications.append(
                f"{datetime.now().isoformat()}: {justification}"
            )
        
        self.task_list.current_task_id = task_id
        task.status = TaskStatus.IN_PROGRESS
        self.task_list.last_updated = datetime.now()
        
        if self.config.auto_save:
            self._save_tasks()
        
        _LOG.info(f"Switched to task {task_id} with justification")
        return True
    
    def _get_task_by_id(self, task_id: str) -> Optional[Task]:
        """Get task by ID."""
        for task in self.task_list.tasks:
            if task.id == task_id:
                return task
        return None
    
    def _get_next_pending_task(self) -> Optional[Task]:
        """Get next pending task by priority."""
        pending_tasks = [
            t for t in self.task_list.tasks 
            if t.status == TaskStatus.PENDING
        ]
        return pending_tasks[0] if pending_tasks else None
    
    def _save_tasks(self) -> None:
        """
        Persist tasks to local storage.
        
        Requirement: F-1, F-13
        Version: v01
        """
        try:
            self.config.tasks_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                "tasks": [
                    {
                        "id": t.id,
                        "title": t.title,
                        "priority": t.priority,
                        "status": t.status.value,
                        "created_at": t.created_at.isoformat(),
                        "completed_at": t.completed_at.isoformat() if t.completed_at else None,
                        "justifications": t.justifications
                    }
                    for t in self.task_list.tasks
                ],
                "current_task_id": self.task_list.current_task_id,
                "last_updated": self.task_list.last_updated.isoformat()
            }
            
            with open(self.config.tasks_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            _LOG.debug(f"Saved {len(self.task_list.tasks)} tasks")
        except Exception as e:
            _LOG.error(f"Failed to save tasks: {e}")
    
    def _load_tasks(self) -> None:
        """
        Load tasks from local storage.
        
        Requirement: F-1
        Version: v01
        """
        if not self.config.tasks_file.exists():
            _LOG.info("No existing tasks file found")
            return
        
        try:
            with open(self.config.tasks_file, 'r') as f:
                data = json.load(f)
            
            self.task_list.tasks.clear()
            
            for task_data in data.get("tasks", []):
                task = Task(
                    id=task_data["id"],
                    title=task_data["title"],
                    priority=task_data["priority"],
                    status=TaskStatus(task_data["status"]),
                    created_at=datetime.fromisoformat(task_data["created_at"]),
                    completed_at=datetime.fromisoformat(task_data["completed_at"]) 
                        if task_data.get("completed_at") else None,
                    justifications=task_data.get("justifications", [])
                )
                self.task_list.tasks.append(task)
            
            self.task_list.current_task_id = data.get("current_task_id")
            self.task_list.last_updated = datetime.fromisoformat(data["last_updated"])
            
            _LOG.info(f"Loaded {len(self.task_list.tasks)} tasks")
        except Exception as e:
            _LOG.error(f"Failed to load tasks: {e}")
```

## API Specification

### TaskManager Class

**Methods:**
- `import_tasks(task_descriptions: list[str]) -> None` - Import new task list
- `current_task: Optional[Task]` - Get current active task (property)
- `update_task_order(task_id: str, new_priority: int) -> bool` - Reorder tasks
- `complete_task(task_id: str) -> bool` - Mark task as completed
- `switch_task(task_id: str, justification: str) -> bool` - Switch active task with justification

### Task Model

**Fields:**
- `id: str` - Unique identifier
- `title: str` - Task description
- `priority: int` - Task priority (1 = highest)
- `status: TaskStatus` - Current status
- `created_at: datetime` - Creation timestamp
- `completed_at: Optional[datetime]` - Completion timestamp
- `justifications: list[str]` - Task switch justifications

## Example Usage

```python
from src.task_manager import TaskManager, TaskConfig

# Initialize
config = TaskConfig(tasks_file=Path("./data/tasks.json"))
manager = TaskManager(config)

# Import tasks
manager.import_tasks([
    "Complete PRD documentation",
    "Review code implementation", 
    "Run integration tests",
    "Deploy to staging"
])

# Get current task
current = manager.current_task
print(f"Current task: {current.title} (Priority {current.priority})")

# Switch task with justification
manager.switch_task(
    task_id=manager.task_list.tasks[2].id,
    justification="Urgent bug found in tests"
)

# Complete task
manager.complete_task(current.id)
```

## Version History

- **v01** (2025-06-15): Initial implementation
  - Basic task import, tracking, and persistence
  - Priority-based ordering
  - Task switching with justifications
  - JSON-based local storage
# Output Examples

This directory contains example outputs from the Productivity Guard system to demonstrate the types of data collected and analyzed.

## Files

### `sample_tasks.json`
Example task list showing the structure of task data including:
- Task IDs (UUID format)
- Descriptions
- Priority levels (P1, P2, P3)
- Status (pending, in_progress, completed)
- Timestamps for creation, updates, and completion

### `sample_analysis_log.jsonl`
Example AI analysis log in JSON Lines format showing:
- Screenshot analysis results
- On-task/off-task detection
- Confidence scores
- Reasons for classification
- Justification requests and responses
- Escalation levels

### `example_analysis_results.json`
Example aggregated analysis results showing:
- Daily productivity summary
- Task-specific productivity metrics
- Distraction patterns
- Escalation events
- AI-generated recommendations

## Screenshot Examples

The system captures screenshots at regular intervals. Screenshots are organized by date and include:
- Timestamp in filename (HHMMSS-microseconds-sequence.png)
- Full screen captures
- Stored in daily directories (e.g., 2025JUN15/)

Example screenshot naming:
- `101530-123456-0001.png` - Captured at 10:15:30
- `102045-234567-0002.png` - Captured at 10:20:45

## Data Flow

1. **Screenshots** are captured every 120 seconds
2. **AI Analysis** runs every 300-600 seconds (based on escalation)
3. **Logs** are written in real-time to JSONL files
4. **Tasks** are persisted to JSON for session continuity
5. **Reports** can be generated from accumulated data

## Privacy Note

All data is stored locally. No screenshots or analysis results are transmitted to external servers. The AI analysis happens through API calls that don't include the actual screenshot content.
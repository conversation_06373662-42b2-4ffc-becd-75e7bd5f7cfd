Screenshot Directory Structure
=============================

Screenshots are organized by date in the following format:

/screenshots/
  /2025JUN15/        # Date directory (YYYYMMMDD)
    101530-123456-0001.png   # HHMMSS-microseconds-sequence.png
    102045-234567-0002.png
    103000-345678-0003.png
    ...

Each screenshot filename contains:
- HHMMSS: Time of capture (24-hour format)
- microseconds: Microsecond precision for uniqueness
- sequence: Daily sequence number

The system captures screenshots from all configured displays at regular intervals
(default: every 120 seconds) and stores them in daily directories for easy
organization and cleanup.

Note: Actual screenshot files are not included in this example directory to
preserve privacy and reduce repository size. In production, these would be
full-resolution captures of the user's screen(s).
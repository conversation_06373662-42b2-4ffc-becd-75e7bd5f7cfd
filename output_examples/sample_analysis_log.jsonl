{"timestamp": "2025-06-15T10:15:30Z", "screenshot_path": "screenshots/2025JUN15/101530-123456-0001.png", "task_description": "Complete quarterly financial report", "on_task": true, "confidence": 0.95, "reason": "User is actively working in Excel with financial data visible", "justification_required": false}
{"timestamp": "2025-06-15T10:20:45Z", "screenshot_path": "screenshots/2025JUN15/102045-234567-0002.png", "task_description": "Complete quarterly financial report", "on_task": true, "confidence": 0.92, "reason": "User is reviewing financial charts and graphs", "justification_required": false}
{"timestamp": "2025-06-15T10:25:00Z", "screenshot_path": "screenshots/2025JUN15/102500-345678-0003.png", "task_description": "Complete quarterly financial report", "on_task": false, "confidence": 0.87, "reason": "User browsing social media (Twitter detected)", "justification_required": true, "justification_provided": true, "justification": "Taking a 5-minute mental break before diving into complex calculations"}
{"timestamp": "2025-06-15T10:30:15Z", "screenshot_path": "screenshots/2025JUN15/103015-456789-0004.png", "task_description": "Complete quarterly financial report", "on_task": true, "confidence": 0.91, "reason": "User back to working on financial spreadsheet", "justification_required": false}
{"timestamp": "2025-06-15T10:35:30Z", "screenshot_path": "screenshots/2025JUN15/103530-567890-0005.png", "task_description": "Review and merge pull request #245", "on_task": true, "confidence": 0.94, "reason": "User viewing code diff in GitHub", "justification_required": false}
{"timestamp": "2025-06-15T10:40:45Z", "screenshot_path": "screenshots/2025JUN15/104045-678901-0006.png", "task_description": "Review and merge pull request #245", "on_task": false, "confidence": 0.83, "reason": "User watching YouTube video", "justification_required": true, "justification_provided": false, "escalation_level": "WARNING"}
{"timestamp": "2025-06-15T10:45:00Z", "screenshot_path": "screenshots/2025JUN15/104500-789012-0007.png", "task_description": "Review and merge pull request #245", "on_task": false, "confidence": 0.86, "reason": "User still on YouTube", "justification_required": true, "justification_provided": false, "escalation_level": "FREQUENT"}
{"timestamp": "2025-06-15T10:50:15Z", "screenshot_path": "screenshots/2025JUN15/105015-890123-0008.png", "task_description": "Review and merge pull request #245", "on_task": true, "confidence": 0.93, "reason": "User back to reviewing code in IDE", "justification_required": false, "escalation_level": "NORMAL"}
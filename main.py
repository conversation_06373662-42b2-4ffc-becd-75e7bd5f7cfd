#!/usr/bin/env python3
"""Productivity Guard - AI-driven focus companion."""

import sys
import asyncio
from dotenv import load_dotenv
import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

from src.interfaces import SystemConfig
from src.main_app import ProductivityGuardApp
from src.task_manager.manager import TaskManager
from src.task_manager.config import TaskManagerConfig

# Initialize console for rich output
console = Console()

# Create the main Typer app
app = typer.Typer(
    name="productivity-guard",
    help="AI-driven focus companion that helps you stay on task",
    rich_markup_mode="rich"
)

def print_banner():
    """Print the application banner."""
    banner = Panel.fit(
        "[bold blue]Productivity Guard[/bold blue]\n[dim]AI-driven focus companion[/dim]",
        border_style="blue"
    )
    console.print(banner)

@app.command()
def start(
    demo: bool = typer.Option(False, "--demo", help="Start with demo tasks")
):
    """Start the productivity monitoring system."""
    print_banner()

    # Load environment variables
    load_dotenv()

    # Create configuration
    config = SystemConfig()

    # Adjust config for better demo experience
    if demo:
        config.screenshot_interval = 5  # More frequent screenshots for demo
        config.main_ai_interval_one = 10  # More frequent AI checks
        config.screens_to_capture = [1, 2]  # Only capture available screens

        # Add demo tasks
        task_config = TaskManagerConfig(tasks_file=config.output_dir / "tasks.json")
        task_manager = TaskManager(config=task_config)

        # Add some demo tasks
        demo_tasks = [
            "Complete PRD verification and testing",
            "Review system architecture",
            "Write comprehensive documentation"
        ]

        for i, task_desc in enumerate(demo_tasks, 1):
            task_manager.add_task(task_desc, priority=i)

        console.print(f"[green]Added {len(demo_tasks)} demo tasks[/green]")
    else:
        # Use safer defaults for production
        config.screens_to_capture = [1, 2]  # Only capture available screens

    console.print("\n[bold]Starting monitoring system...[/bold]")
    console.print(f"Screenshot interval: {config.screenshot_interval}s")
    console.print(f"AI check interval: {config.main_ai_interval_one}s")
    console.print(f"Output directory: {config.output_dir}")

    # Start the application
    try:
        app_instance = ProductivityGuardApp(config)
        asyncio.run(app_instance.start())
    except KeyboardInterrupt:
        console.print("\n[yellow]Monitoring stopped by user[/yellow]")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)

@app.command()
def tasks():
    """List all current tasks."""
    load_dotenv()
    config = SystemConfig()
    task_config = TaskManagerConfig(tasks_file=config.output_dir / "tasks.json")
    task_manager = TaskManager(config=task_config)

    all_tasks = task_manager.get_all_tasks()

    if not all_tasks:
        console.print("[yellow]No tasks found. Use 'add' command to create tasks.[/yellow]")
        return

    table = Table(title="Current Tasks")
    table.add_column("Priority", style="cyan", no_wrap=True)
    table.add_column("Description", style="white")
    table.add_column("Status", style="green")

    for task in all_tasks:
        status = "✓ Complete" if task.status.value == "completed" else "○ Active"
        table.add_row(str(task.priority), task.description, status)

    console.print(table)

@app.command()
def add(description: str):
    """Add a new task."""
    load_dotenv()
    config = SystemConfig()
    task_config = TaskManagerConfig(tasks_file=config.output_dir / "tasks.json")
    task_manager = TaskManager(config=task_config)

    task = task_manager.add_task(description)
    console.print(f"[green]Added task: {task.description}[/green]")

@app.command()
def demo():
    """Load demo tasks for testing."""
    load_dotenv()
    config = SystemConfig()
    task_config = TaskManagerConfig(tasks_file=config.output_dir / "tasks.json")
    task_manager = TaskManager(config=task_config)

    demo_tasks = [
        "Complete PRD verification and testing",
        "Review system architecture",
        "Write comprehensive documentation",
        "Test all system components",
        "Prepare deployment checklist"
    ]

    for i, task_desc in enumerate(demo_tasks, 1):
        task_manager.add_task(task_desc, priority=i)

    console.print(f"[green]Added {len(demo_tasks)} demo tasks[/green]")

    # Show the tasks
    tasks()

if __name__ == "__main__":
    app()

#!/usr/bin/env python3
"""
Test runner script for the Feisty4 test suite.

This script provides an easy way to run all tests or specific test modules
with various options.
"""

import sys
import subprocess
from pathlib import Path
from typing import List, Optional


def run_tests(
    test_paths: Optional[List[str]] = None,
    verbose: bool = True,
    coverage: bool = False,
    markers: Optional[str] = None,
    parallel: bool = False
) -> int:
    """
    Run pytest with specified options.
    
    Args:
        test_paths: Specific test files or directories to run
        verbose: Enable verbose output
        coverage: Generate coverage report
        markers: Run only tests matching given markers
        parallel: Run tests in parallel
        
    Returns:
        Exit code from pytest
    """
    cmd = ["python", "-m", "pytest"]
    
    # Add test paths or default to tests directory
    if test_paths:
        cmd.extend(test_paths)
    else:
        cmd.append("tests/")
    
    # Add options
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend([
            "--cov=src",
            "--cov-report=html",
            "--cov-report=term-missing"
        ])
    
    if markers:
        cmd.extend(["-m", markers])
    
    if parallel:
        cmd.extend(["-n", "auto"])
    
    # Additional useful options
    cmd.extend([
        "--tb=short",  # Shorter traceback format
        "--strict-markers",  # Ensure all markers are registered
        "--durations=10",  # Show 10 slowest tests
    ])
    
    print(f"Running: {' '.join(cmd)}")
    print("-" * 80)
    
    return subprocess.call(cmd)


def main():
    """Main entry point for test runner."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Run tests for the Feisty4 productivity guard system"
    )
    
    parser.add_argument(
        "tests",
        nargs="*",
        help="Specific test files or directories to run (default: all tests)"
    )
    
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        default=True,
        help="Verbose output (default: True)"
    )
    
    parser.add_argument(
        "-q", "--quiet",
        action="store_true",
        help="Quiet output (overrides verbose)"
    )
    
    parser.add_argument(
        "-c", "--coverage",
        action="store_true",
        help="Generate coverage report"
    )
    
    parser.add_argument(
        "-m", "--markers",
        help="Run only tests matching given markers (e.g., 'not slow')"
    )
    
    parser.add_argument(
        "-p", "--parallel",
        action="store_true",
        help="Run tests in parallel (requires pytest-xdist)"
    )
    
    parser.add_argument(
        "--unit",
        action="store_true",
        help="Run only unit tests (excludes integration tests)"
    )
    
    parser.add_argument(
        "--integration",
        action="store_true",
        help="Run only integration tests"
    )
    
    parser.add_argument(
        "--module",
        choices=["task_manager", "local_store", "user_interface", 
                 "reasoning_ai", "escalator", "integration"],
        help="Run tests for a specific module"
    )
    
    args = parser.parse_args()
    
    # Handle quiet flag
    verbose = args.verbose and not args.quiet
    
    # Build test paths based on options
    test_paths = args.tests
    
    if args.module:
        test_paths = [f"tests/test_{args.module}.py"]
    elif args.unit:
        # All test files except integration
        test_paths = [
            "tests/test_task_manager.py",
            "tests/test_local_store.py", 
            "tests/test_user_interface.py",
            "tests/test_reasoning_ai.py",
            "tests/test_escalator.py"
        ]
    elif args.integration:
        test_paths = ["tests/test_integration.py"]
    
    # Run tests
    exit_code = run_tests(
        test_paths=test_paths,
        verbose=verbose,
        coverage=args.coverage,
        markers=args.markers,
        parallel=args.parallel
    )
    
    # Print summary
    print("\n" + "-" * 80)
    if exit_code == 0:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
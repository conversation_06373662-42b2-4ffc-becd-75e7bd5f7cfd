[project]
name = "feisty"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12.0"
dependencies = [
    "loguru>=0.7.3",
    "mss>=10.0.0",
    "openai>=1.86.0",
    "pillow>=11.2.1",
    "pyautogui>=0.9.54",
    "pynput>=1.8.1",
    "python-dotenv>=1.1.0",
    "rich>=14.0.0",
    "typer[all]>=0.16.0",
]

[project.optional-dependencies]
test = [
    "pytest>=8.0.0",
    "pytest-cov>=5.0.0",
    "pytest-mock>=3.14.0",
]

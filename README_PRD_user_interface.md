# User Interface Module PRD

## Overview
The User Interface module provides both CLI and chat interfaces for users to interact with the Productivity Guard system, manage tasks, respond to prompts, and configure settings.

## Requirements Mapping

| PRD Requirement | Description | Implementation |
|----------------|-------------|----------------|
| **F-1** | Import daily task list (CLI or chat) | `CLI.import_tasks()`, `ChatInterface.handle_task_input()` |
| **F-3** | Allow user to update task order or mark complete | `CLI.update_task()`, `ChatInterface.handle_task_command()` |
| **F-8** | Accept justification text | `ChatInterface.handle_justification()` |
| **N-2** | Modular with TypeHinted interfaces | All methods with type hints, passes mypy --strict |
| **N-3** | Configurable interface settings | `UIConfig` dataclass |

## Implementation Approach

```python
# ── /src/user_interface/config.py ───────────────────────────────────────────────
from __future__ import annotations

from dataclasses import dataclass
from typing import Final, Literal

__all__: Final = ["UIConfig"]

@dataclass(slots=True)
class UIConfig:
    """
    Configuration for user interface.
    
    Requirement: N-3
    Version: v01
    """
    interface_type: Literal["cli", "chat", "both"] = "both"
    chat_port: int = 8080
    chat_host: str = "localhost"
    cli_colors: bool = True
    prompt_timeout: int = 30  # seconds for user input timeout
    notification_style: Literal["minimal", "detailed"] = "detailed"
    auto_start: bool = False
```

```python
# ── /src/user_interface/models.py ───────────────────────────────────────────────
from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Optional

__all__ = ["CommandType", "UserCommand", "ChatMessage", "InterfaceEvent"]

class CommandType(Enum):
    """Types of user commands."""
    IMPORT_TASKS = "import_tasks"
    LIST_TASKS = "list_tasks"
    UPDATE_TASK = "update_task"
    COMPLETE_TASK = "complete_task"
    SWITCH_TASK = "switch_task"
    JUSTIFY = "justify"
    STATUS = "status"
    START = "start"
    STOP = "stop"
    CONFIG = "config"
    HELP = "help"
    EXIT = "exit"

@dataclass
class UserCommand:
    """
    Parsed user command.
    
    Requirement: F-1, F-3
    Version: v01
    """
    command_type: CommandType
    args: list[str]
    kwargs: dict[str, Any]
    raw_input: str
    timestamp: datetime

@dataclass
class ChatMessage:
    """
    Chat interface message.
    
    Requirement: F-8
    Version: v01
    """
    id: str
    sender: Literal["user", "system"]
    content: str
    timestamp: datetime
    message_type: Literal["text", "prompt", "notification"]
    metadata: dict[str, Any]

@dataclass
class InterfaceEvent:
    """
    UI event for logging.
    
    Requirement: N-2
    Version: v01
    """
    event_type: str
    interface: Literal["cli", "chat"]
    command: Optional[CommandType]
    success: bool
    details: dict[str, Any]
    timestamp: datetime
```

```python
# ── /src/user_interface/cli.py ──────────────────────────────────────────────────
from __future__ import annotations

import argparse
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Callable, Optional

from .config import UIConfig
from .models import CommandType, UserCommand

__all__ = ["CLI"]

_LOG = logging.getLogger(__name__)

class CLI:
    """
    Command-line interface for Productivity Guard.
    
    Requirement: F-1, F-3, N-2
    Version: v01
    """
    
    def __init__(
        self,
        config: UIConfig,
        on_command: Optional[Callable[[UserCommand], Any]] = None
    ) -> None:
        """
        Initialize CLI.
        
        Requirement: N-2, N-3
        Version: v01
        """
        self.config = config
        self.on_command = on_command
        self.parser = self._create_parser()
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """Create argument parser with all commands."""
        parser = argparse.ArgumentParser(
            prog="productivity-guard",
            description="AI-powered productivity companion"
        )
        
        subparsers = parser.add_subparsers(dest="command", help="Available commands")
        
        # Import tasks command
        import_parser = subparsers.add_parser(
            "import", 
            help="Import task list"
        )
        import_parser.add_argument(
            "tasks",
            nargs="+",
            help="Task descriptions (quoted strings)"
        )
        import_parser.add_argument(
            "--file", "-f",
            type=Path,
            help="Import from file (one task per line)"
        )
        
        # List tasks command
        subparsers.add_parser("list", help="List all tasks")
        
        # Update task command
        update_parser = subparsers.add_parser(
            "update",
            help="Update task priority"
        )
        update_parser.add_argument("task_id", help="Task ID")
        update_parser.add_argument("priority", type=int, help="New priority")
        
        # Complete task command
        complete_parser = subparsers.add_parser(
            "complete",
            help="Mark task as completed"
        )
        complete_parser.add_argument("task_id", help="Task ID")
        
        # Switch task command
        switch_parser = subparsers.add_parser(
            "switch",
            help="Switch to different task"
        )
        switch_parser.add_argument("task_id", help="Task ID")
        switch_parser.add_argument(
            "--justify", "-j",
            required=True,
            help="Justification for switch"
        )
        
        # Status command
        subparsers.add_parser("status", help="Show current status")
        
        # Start/stop commands
        subparsers.add_parser("start", help="Start monitoring")
        subparsers.add_parser("stop", help="Stop monitoring")
        
        # Config command
        config_parser = subparsers.add_parser(
            "config",
            help="View or update configuration"
        )
        config_parser.add_argument(
            "--set",
            nargs=2,
            metavar=("KEY", "VALUE"),
            help="Set configuration value"
        )
        config_parser.add_argument(
            "--get",
            metavar="KEY",
            help="Get configuration value"
        )
        
        # Help command
        subparsers.add_parser("help", help="Show help message")
        
        return parser
    
    def run(self, args: Optional[list[str]] = None) -> int:
        """
        Run CLI with given arguments.
        
        Requirement: F-1, F-3
        Version: v01
        """
        try:
            parsed_args = self.parser.parse_args(args)
            
            if not parsed_args.command:
                self.parser.print_help()
                return 0
            
            command = self._create_command(parsed_args)
            
            if self.on_command:
                result = self.on_command(command)
                self._handle_result(result)
            else:
                _LOG.warning("No command handler configured")
            
            return 0
            
        except SystemExit as e:
            return e.code if isinstance(e.code, int) else 1
        except Exception as e:
            self._print_error(f"Command failed: {e}")
            return 1
    
    def _create_command(self, args: argparse.Namespace) -> UserCommand:
        """Convert parsed args to UserCommand."""
        command_map = {
            "import": CommandType.IMPORT_TASKS,
            "list": CommandType.LIST_TASKS,
            "update": CommandType.UPDATE_TASK,
            "complete": CommandType.COMPLETE_TASK,
            "switch": CommandType.SWITCH_TASK,
            "status": CommandType.STATUS,
            "start": CommandType.START,
            "stop": CommandType.STOP,
            "config": CommandType.CONFIG,
            "help": CommandType.HELP
        }
        
        command_type = command_map.get(args.command, CommandType.HELP)
        
        # Extract args and kwargs
        cmd_args = []
        cmd_kwargs = {}
        
        for key, value in vars(args).items():
            if key == "command":
                continue
            
            if value is not None:
                if key in ["tasks", "task_id", "priority"]:
                    if isinstance(value, list):
                        cmd_args.extend(value)
                    else:
                        cmd_args.append(value)
                else:
                    cmd_kwargs[key] = value
        
        return UserCommand(
            command_type=command_type,
            args=cmd_args,
            kwargs=cmd_kwargs,
            raw_input=" ".join(sys.argv[1:]),
            timestamp=datetime.now()
        )
    
    def _handle_result(self, result: Any) -> None:
        """Display command result."""
        if result is None:
            return
        
        if isinstance(result, str):
            self._print_info(result)
        elif isinstance(result, dict):
            if result.get("success"):
                self._print_success(result.get("message", "Command completed"))
            else:
                self._print_error(result.get("message", "Command failed"))
        elif isinstance(result, list):
            for item in result:
                self._print_info(str(item))
    
    def _print_info(self, message: str) -> None:
        """Print info message with optional color."""
        if self.config.cli_colors:
            print(f"\033[0m{message}\033[0m")
        else:
            print(message)
    
    def _print_success(self, message: str) -> None:
        """Print success message."""
        if self.config.cli_colors:
            print(f"\033[32m✓ {message}\033[0m")
        else:
            print(f"✓ {message}")
    
    def _print_error(self, message: str) -> None:
        """Print error message."""
        if self.config.cli_colors:
            print(f"\033[31m✗ {message}\033[0m", file=sys.stderr)
        else:
            print(f"✗ {message}", file=sys.stderr)
```

```python
# ── /src/user_interface/chat.py ─────────────────────────────────────────────────
from __future__ import annotations

import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Any, Callable, Optional

import websockets
from websockets.server import WebSocketServerProtocol

from .config import UIConfig
from .models import ChatMessage, CommandType, UserCommand

__all__ = ["ChatInterface"]

_LOG = logging.getLogger(__name__)

class ChatInterface:
    """
    WebSocket-based chat interface.
    
    Requirement: F-1, F-3, F-8, N-2
    Version: v01
    """
    
    def __init__(
        self,
        config: UIConfig,
        on_command: Optional[Callable[[UserCommand], Any]] = None,
        on_justification: Optional[Callable[[str, str], Any]] = None
    ) -> None:
        """
        Initialize chat interface.
        
        Requirement: N-2, N-3
        Version: v01
        """
        self.config = config
        self.on_command = on_command
        self.on_justification = on_justification
        self.active_connections: set[WebSocketServerProtocol] = set()
        self.pending_prompts: dict[str, ChatMessage] = {}
    
    async def start_server(self) -> None:
        """
        Start WebSocket server.
        
        Requirement: F-8
        Version: v01
        """
        async with websockets.serve(
            self.handle_connection,
            self.config.chat_host,
            self.config.chat_port
        ):
            _LOG.info(
                f"Chat interface started on "
                f"{self.config.chat_host}:{self.config.chat_port}"
            )
            await asyncio.Future()  # Run forever
    
    async def handle_connection(
        self,
        websocket: WebSocketServerProtocol,
        path: str
    ) -> None:
        """Handle new WebSocket connection."""
        self.active_connections.add(websocket)
        
        try:
            # Send welcome message
            await self.send_message(
                websocket,
                "Welcome to Productivity Guard! Type 'help' for commands.",
                message_type="text"
            )
            
            # Handle messages
            async for message in websocket:
                await self.handle_message(websocket, message)
                
        except websockets.exceptions.ConnectionClosed:
            pass
        finally:
            self.active_connections.remove(websocket)
    
    async def handle_message(
        self,
        websocket: WebSocketServerProtocol,
        message: str
    ) -> None:
        """
        Process incoming chat message.
        
        Requirement: F-1, F-3, F-8
        Version: v01
        """
        try:
            data = json.loads(message)
            content = data.get("content", "").strip()
            message_id = data.get("id", str(uuid.uuid4()))
            
            # Check if this is a justification response
            if data.get("type") == "justification":
                prompt_id = data.get("prompt_id")
                if prompt_id and self.on_justification:
                    result = self.on_justification(prompt_id, content)
                    await self.send_result(websocket, result)
                return
            
            # Parse as command
            command = self._parse_chat_command(content)
            
            if command and self.on_command:
                result = self.on_command(command)
                await self.send_result(websocket, result)
            else:
                await self.send_message(
                    websocket,
                    "Unknown command. Type 'help' for available commands.",
                    message_type="text"
                )
                
        except json.JSONDecodeError:
            await self.send_message(
                websocket,
                "Invalid message format. Please send JSON.",
                message_type="text"
            )
        except Exception as e:
            _LOG.error(f"Error handling message: {e}")
            await self.send_message(
                websocket,
                f"Error: {e}",
                message_type="text"
            )
    
    async def send_prompt(
        self,
        prompt_text: str,
        metadata: Optional[dict[str, Any]] = None
    ) -> str:
        """
        Send prompt to all connected clients.
        
        Requirement: F-7, F-8
        Version: v01
        """
        prompt_id = str(uuid.uuid4())
        
        message = ChatMessage(
            id=prompt_id,
            sender="system",
            content=prompt_text,
            timestamp=datetime.now(),
            message_type="prompt",
            metadata=metadata or {}
        )
        
        self.pending_prompts[prompt_id] = message
        
        # Send to all connections
        await self.broadcast_message(message)
        
        return prompt_id
    
    async def broadcast_message(self, message: ChatMessage) -> None:
        """Send message to all connected clients."""
        message_data = {
            "id": message.id,
            "sender": message.sender,
            "content": message.content,
            "timestamp": message.timestamp.isoformat(),
            "type": message.message_type,
            "metadata": message.metadata
        }
        
        # Send to all active connections
        if self.active_connections:
            await asyncio.gather(
                *[
                    websocket.send(json.dumps(message_data))
                    for websocket in self.active_connections
                ],
                return_exceptions=True
            )
    
    async def send_message(
        self,
        websocket: WebSocketServerProtocol,
        content: str,
        message_type: str = "text"
    ) -> None:
        """Send message to specific client."""
        message_data = {
            "id": str(uuid.uuid4()),
            "sender": "system",
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "type": message_type,
            "metadata": {}
        }
        
        await websocket.send(json.dumps(message_data))
    
    async def send_result(
        self,
        websocket: WebSocketServerProtocol,
        result: Any
    ) -> None:
        """Send command result to client."""
        if isinstance(result, str):
            await self.send_message(websocket, result)
        elif isinstance(result, dict):
            content = result.get("message", "Command completed")
            if result.get("success"):
                await self.send_message(websocket, f"✓ {content}")
            else:
                await self.send_message(websocket, f"✗ {content}")
        elif isinstance(result, list):
            content = "\n".join(str(item) for item in result)
            await self.send_message(websocket, content)
    
    def _parse_chat_command(self, text: str) -> Optional[UserCommand]:
        """Parse chat text into command."""
        if not text:
            return None
        
        parts = text.split()
        if not parts:
            return None
        
        command_word = parts[0].lower()
        
        # Command mappings
        command_map = {
            "import": CommandType.IMPORT_TASKS,
            "tasks": CommandType.LIST_TASKS,
            "list": CommandType.LIST_TASKS,
            "update": CommandType.UPDATE_TASK,
            "complete": CommandType.COMPLETE_TASK,
            "done": CommandType.COMPLETE_TASK,
            "switch": CommandType.SWITCH_TASK,
            "status": CommandType.STATUS,
            "start": CommandType.START,
            "stop": CommandType.STOP,
            "help": CommandType.HELP
        }
        
        command_type = command_map.get(command_word)
        if not command_type:
            return None
        
        # Parse arguments
        args = parts[1:]
        kwargs = {}
        
        # Special handling for task import
        if command_type == CommandType.IMPORT_TASKS and args:
            # Join all args as task descriptions
            args = [" ".join(args)]
        
        # Special handling for switch command
        if command_type == CommandType.SWITCH_TASK and len(args) >= 2:
            kwargs["justify"] = " ".join(args[1:])
            args = [args[0]]
        
        return UserCommand(
            command_type=command_type,
            args=args,
            kwargs=kwargs,
            raw_input=text,
            timestamp=datetime.now()
        )
```

```python
# ── /src/user_interface/interface.py ────────────────────────────────────────────
from __future__ import annotations

import asyncio
import logging
import threading
from typing import Any, Callable, Optional

from .chat import ChatInterface
from .cli import CLI
from .config import UIConfig
from .models import InterfaceEvent, UserCommand

__all__ = ["UserInterface"]

_LOG = logging.getLogger(__name__)

class UserInterface:
    """
    Unified interface manager for CLI and chat.
    
    Requirement: F-1, F-3, F-8, N-2
    Version: v01
    """
    
    def __init__(
        self,
        config: UIConfig,
        command_handler: Optional[Callable[[UserCommand], Any]] = None,
        justification_handler: Optional[Callable[[str, str], Any]] = None
    ) -> None:
        """
        Initialize user interface.
        
        Requirement: N-2, N-3
        Version: v01
        """
        self.config = config
        self.command_handler = command_handler
        self.justification_handler = justification_handler
        
        # Initialize interfaces based on config
        self.cli: Optional[CLI] = None
        self.chat: Optional[ChatInterface] = None
        
        if config.interface_type in ["cli", "both"]:
            self.cli = CLI(config, self._handle_command)
        
        if config.interface_type in ["chat", "both"]:
            self.chat = ChatInterface(
                config,
                self._handle_command,
                self._handle_justification
            )
        
        self._chat_thread: Optional[threading.Thread] = None
        self._events: list[InterfaceEvent] = []
    
    def start(self) -> None:
        """
        Start configured interfaces.
        
        Requirement: F-1
        Version: v01
        """
        if self.chat and self.config.auto_start:
            self._start_chat_server()
        
        _LOG.info(f"User interface started (type: {self.config.interface_type})")
    
    def stop(self) -> None:
        """Stop all interfaces."""
        # Chat server stops when thread ends
        _LOG.info("User interface stopped")
    
    def run_cli_command(self, args: list[str]) -> int:
        """
        Execute CLI command.
        
        Requirement: F-1, F-3
        Version: v01
        """
        if not self.cli:
            _LOG.error("CLI interface not configured")
            return 1
        
        return self.cli.run(args)
    
    async def send_prompt(
        self,
        prompt_text: str,
        metadata: Optional[dict[str, Any]] = None
    ) -> Optional[str]:
        """
        Send prompt through chat interface.
        
        Requirement: F-7, F-8
        Version: v01
        """
        if not self.chat:
            _LOG.warning("Chat interface not configured for prompts")
            return None
        
        return await self.chat.send_prompt(prompt_text, metadata)
    
    def get_help_text(self) -> str:
        """Get formatted help text."""
        help_sections = [
            "# Productivity Guard Commands",
            "",
            "## Task Management",
            "- `import <task1> <task2> ...` - Import task list",
            "- `list` or `tasks` - Show all tasks",
            "- `update <task_id> <priority>` - Change task priority",
            "- `complete <task_id>` or `done <task_id>` - Mark task complete",
            "- `switch <task_id> <justification>` - Switch active task",
            "",
            "## Monitoring",
            "- `start` - Start productivity monitoring",
            "- `stop` - Stop productivity monitoring",
            "- `status` - Show current status",
            "",
            "## Configuration",
            "- `config --get <key>` - Get config value",
            "- `config --set <key> <value>` - Set config value",
            "",
            "## Other",
            "- `help` - Show this help message"
        ]
        
        return "\n".join(help_sections)
    
    def _handle_command(self, command: UserCommand) -> Any:
        """
        Process command from any interface.
        
        Requirement: F-1, F-3
        Version: v01
        """
        # Log event
        event = InterfaceEvent(
            event_type="command",
            interface="cli" if command.raw_input else "chat",
            command=command.command_type,
            success=False,
            details={"args": command.args, "kwargs": command.kwargs},
            timestamp=command.timestamp
        )
        
        try:
            # Handle help specially
            if command.command_type == CommandType.HELP:
                result = self.get_help_text()
            elif self.command_handler:
                result = self.command_handler(command)
            else:
                result = {"success": False, "message": "No command handler configured"}
            
            event.success = True
            self._events.append(event)
            
            return result
            
        except Exception as e:
            _LOG.error(f"Command handling failed: {e}")
            event.success = False
            event.details["error"] = str(e)
            self._events.append(event)
            
            return {"success": False, "message": f"Error: {e}"}
    
    def _handle_justification(self, prompt_id: str, text: str) -> Any:
        """
        Process justification response.
        
        Requirement: F-8
        Version: v01
        """
        event = InterfaceEvent(
            event_type="justification",
            interface="chat",
            command=None,
            success=False,
            details={"prompt_id": prompt_id, "text_length": len(text)},
            timestamp=datetime.now()
        )
        
        try:
            if self.justification_handler:
                result = self.justification_handler(prompt_id, text)
            else:
                result = {"success": False, "message": "No justification handler"}
            
            event.success = True
            self._events.append(event)
            
            return result
            
        except Exception as e:
            _LOG.error(f"Justification handling failed: {e}")
            event.success = False
            event.details["error"] = str(e)
            self._events.append(event)
            
            return {"success": False, "message": f"Error: {e}"}
    
    def _start_chat_server(self) -> None:
        """Start chat server in background thread."""
        def run_chat():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                loop.run_until_complete(self.chat.start_server())
            except Exception as e:
                _LOG.error(f"Chat server error: {e}")
        
        self._chat_thread = threading.Thread(
            target=run_chat,
            name="ChatServer",
            daemon=True
        )
        self._chat_thread.start()
```

## API Specification

### UserInterface Class

**Methods:**
- `start() -> None` - Start configured interfaces
- `stop() -> None` - Stop all interfaces
- `run_cli_command(args: list[str]) -> int` - Execute CLI command
- `send_prompt(prompt_text: str, metadata: dict) -> Optional[str]` - Send chat prompt
- `get_help_text() -> str` - Get formatted help

### CLI Class

**Methods:**
- `run(args: Optional[list[str]]) -> int` - Run CLI with arguments

### ChatInterface Class

**Methods:**
- `start_server() -> None` - Start WebSocket server
- `send_prompt(prompt_text: str, metadata: dict) -> str` - Send prompt to clients
- `broadcast_message(message: ChatMessage) -> None` - Broadcast to all clients

## Example Usage

### CLI Usage

```bash
# Import tasks
productivity-guard import "Complete PRD documentation" "Review code" "Write tests"

# List tasks
productivity-guard list

# Update task priority
productivity-guard update task-123 2

# Complete task
productivity-guard complete task-123

# Switch task with justification
productivity-guard switch task-456 --justify "Urgent bug reported by customer"

# Start monitoring
productivity-guard start

# Check status
productivity-guard status
```

### Chat Interface Usage

```python
from src.user_interface import UserInterface, UIConfig

# Initialize
config = UIConfig(
    interface_type="both",
    chat_port=8080,
    cli_colors=True
)

def handle_command(command):
    print(f"Received command: {command.command_type}")
    return {"success": True, "message": "Command processed"}

def handle_justification(prompt_id, text):
    print(f"Justification for {prompt_id}: {text}")
    return {"success": True, "message": "Justification accepted"}

ui = UserInterface(
    config,
    command_handler=handle_command,
    justification_handler=handle_justification
)

# Start interfaces
ui.start()

# Send prompt through chat
await ui.send_prompt(
    "It looks like you've switched tasks. Why?",
    metadata={"task": "Complete PRD", "screenshot": "path/to/screenshot.png"}
)

# Run CLI command
result = ui.run_cli_command(["status"])
```

### WebSocket Client Example

```javascript
// Connect to chat interface
const ws = new WebSocket('ws://localhost:8080');

ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    
    if (message.type === 'prompt') {
        // Show prompt to user
        const response = prompt(message.content);
        
        // Send justification
        ws.send(JSON.stringify({
            type: 'justification',
            prompt_id: message.id,
            content: response
        }));
    }
};

// Send command
ws.send(JSON.stringify({
    content: 'list tasks'
}));
```

## Version History

- **v01** (2025-06-15): Initial implementation
  - CLI with full command set
  - WebSocket-based chat interface
  - Unified command handling
  - Help system and event logging
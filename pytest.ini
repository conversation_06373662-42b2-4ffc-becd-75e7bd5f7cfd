[pytest]
# pytest configuration for Feisty4 test suite

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    asyncio: marks tests as async (auto-applied by pytest-asyncio)
    performance: marks tests that check performance requirements

# Output options
addopts = 
    --strict-markers
    --tb=short
    --verbose
    -ra

# Coverage options (when run with --cov)
# These are used when running: pytest --cov=src
[coverage:run]
source = src
omit = 
    */tests/*
    */test_*
    */__main__.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
    if TYPE_CHECKING:

# Asyncio configuration
asyncio_mode = auto

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Timeout for tests (in seconds)
# Prevents hanging tests
timeout = 300

# Warnings
filterwarnings =
    error
    ignore::UserWarning
    ignore::DeprecationWarning
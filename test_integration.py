#!/usr/bin/env python3
"""
Test script to verify the integrated system can run end-to-end.
"""

import asyncio
import logging
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent))

from src.interfaces import SystemConfig, TaskPriority
from src.main_app import ProductivityGuardApp


async def test_integration():
    """Test the integrated system."""
    print("\n" + "="*60)
    print("PRODUCTIVITY GUARD - INTEGRATION TEST")
    print("="*60)
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create test configuration
    config = SystemConfig(
        screenshot_interval=2,  # Fast for testing
        main_ai_interval_one=5,
        main_ai_interval_two=2,
        screens_to_capture=[1],  # Just capture main screen
        justification_timeout=10,
        output_dir=Path("./output/integration_test")
    )
    
    # Create app
    app = ProductivityGuardApp(config)
    
    print("\n1. Adding test tasks...")
    app.task_manager.add_task("Complete integration testing", TaskPriority.P1)
    app.task_manager.add_task("Fix any bugs found", TaskPriority.P2)
    app.task_manager.add_task("Update documentation", TaskPriority.P3)
    
    tasks = app.task_manager.get_all_tasks()
    print(f"   Added {len(tasks)} tasks")
    
    print("\n2. Testing screenshot capture...")
    try:
        screenshot = await app.screenshot_taker.capture_screenshot(1)
        print(f"   Screenshot captured: {screenshot['path']}")
    except Exception as e:
        print(f"   Screenshot capture failed: {e}")
    
    print("\n3. Testing AI analysis (mock)...")
    current_task = app.task_manager.get_current_task()
    if current_task:
        print(f"   Current task: {current_task.description}")
        # The AI analysis will happen automatically in the main loop
    
    print("\n4. Starting main loop for 15 seconds...")
    print("   Watch for screenshot captures and AI analysis logs")
    print("   Press Ctrl+C to stop early\n")
    
    # Run for 15 seconds
    app_task = asyncio.create_task(app.start())
    
    try:
        await asyncio.sleep(15)
    except KeyboardInterrupt:
        print("\n   Test interrupted by user")
    finally:
        print("\n5. Shutting down...")
        await app.shutdown()
        app_task.cancel()
        try:
            await app_task
        except asyncio.CancelledError:
            pass
    
    print("\n6. Test Summary:")
    print(f"   Output directory: {config.output_dir}")
    print("   - Check screenshots in: output/integration_test/screenshots/")
    print("   - Check logs in: output/integration_test/logs/")
    print("   - Task data in: output/integration_test/tasks.json")
    
    # Check what was created
    if config.output_dir.exists():
        screenshot_count = len(list((config.output_dir / "screenshots").rglob("*.png")))
        print(f"\n   Screenshots captured: {screenshot_count}")
    
    print("\n" + "="*60)
    print("Integration test completed!")
    print("="*60)


if __name__ == "__main__":
    try:
        asyncio.run(test_integration())
    except Exception as e:
        logging.error(f"Integration test failed: {e}", exc_info=True)
        sys.exit(1)
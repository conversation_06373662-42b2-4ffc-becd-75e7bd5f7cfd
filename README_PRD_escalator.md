# Escalator Module PRD

## Overview
The Escalator module handles user prompting, justification collection, and progressive intervention when users deviate from their priority tasks without justification.

## Requirements Mapping

| PRD Requirement | Description | Implementation |
|----------------|-------------|----------------|
| **F-7** | Prompt user "It looks like you've switched tasks. Why?" | `Escalator.prompt_user()` |
| **F-8** | Accept justification text; if valid, log and continue | `Escalator.handle_justification()` |
| **F-9** | If no justification within 60s, enter Escalation Mode | `Escalator.check_timeout()` |
| **F-10** | Reduce main_ai_interval to 1s in escalation | `Escalator.enter_escalation_mode()` |
| **F-11** | Send persistent notifications every 10s | `Escalator.send_notification()` |
| **F-12** | After 3 unanswered prompts, issue cmd+W | `Escalator.execute_intervention()` |
| **N-2** | Modular with TypeHinted interfaces | All methods with type hints, passes mypy --strict |
| **N-3** | Configurable thresholds | `EscalatorConfig` dataclass |

## Implementation Approach

```python
# ── /src/escalator/config.py ────────────────────────────────────────────────────
from __future__ import annotations

from dataclasses import dataclass
from typing import Final

__all__: Final = ["EscalatorConfig"]

@dataclass(slots=True)
class EscalatorConfig:
    """
    Configuration for escalation behavior.
    
    Requirement: N-3
    Version: v01
    """
    justification_timeout: int = 60  # seconds to wait for justification
    notification_interval: int = 10  # seconds between notifications in escalation
    max_unanswered_prompts: int = 3  # prompts before intervention
    intervention_enabled: bool = True  # whether to execute cmd+W
    notification_sound: bool = True  # play sound with notifications
    escalation_ai_interval: int = 1  # AI check interval in escalation mode
```

```python
# ── /src/escalator/models.py ────────────────────────────────────────────────────
from __future__ import annotations

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Optional

__all__ = ["EscalationState", "PromptRecord", "JustificationRecord"]

class EscalationState(Enum):
    """Current escalation status."""
    NORMAL = "normal"
    PROMPTED = "prompted"
    ESCALATED = "escalated"
    INTERVENING = "intervening"

@dataclass
class PromptRecord:
    """
    Record of a user prompt.
    
    Requirement: F-7, F-9
    Version: v01
    """
    id: str
    message: str
    task_title: str
    screenshot_path: str
    prompted_at: datetime
    response_deadline: datetime
    response_received: bool = False
    response_text: Optional[str] = None
    response_at: Optional[datetime] = None

@dataclass
class JustificationRecord:
    """
    Record of user justification.
    
    Requirement: F-8
    Version: v01
    """
    prompt_id: str
    justification_text: str
    is_valid: bool
    validation_reason: str
    submitted_at: datetime
    task_resumed: bool = False
```

```python
# ── /src/escalator/notifier.py ──────────────────────────────────────────────────
from __future__ import annotations

import logging
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

__all__ = ["Notifier"]

_LOG = logging.getLogger(__name__)

class Notifier:
    """
    Handle user notifications across platforms.
    
    Requirement: F-7, F-11
    Version: v01
    """
    
    def __init__(self, sound_enabled: bool = True) -> None:
        """
        Initialize notifier.
        
        Requirement: F-11
        Version: v01
        """
        self.sound_enabled = sound_enabled
        self.platform = sys.platform
    
    def send_prompt(self, message: str, urgency: str = "normal") -> bool:
        """
        Send notification prompt to user.
        
        Requirement: F-7, F-11
        Version: v01
        """
        try:
            if self.platform == "darwin":  # macOS
                self._send_macos_notification(message, urgency)
            elif self.platform.startswith("linux"):
                self._send_linux_notification(message, urgency)
            elif self.platform == "win32":
                self._send_windows_notification(message, urgency)
            else:
                _LOG.warning(f"Unsupported platform for notifications: {self.platform}")
                return False
            
            if self.sound_enabled and urgency == "critical":
                self._play_alert_sound()
            
            _LOG.info(f"Sent notification: {message[:50]}...")
            return True
            
        except Exception as e:
            _LOG.error(f"Failed to send notification: {e}")
            return False
    
    def _send_macos_notification(self, message: str, urgency: str) -> None:
        """Send notification on macOS using osascript."""
        script = f'''
        display notification "{message}" with title "Productivity Guard" sound name "Glass"
        '''
        
        subprocess.run(
            ["osascript", "-e", script],
            capture_output=True,
            text=True,
            check=True
        )
    
    def _send_linux_notification(self, message: str, urgency: str) -> None:
        """Send notification on Linux using notify-send."""
        urgency_flag = "--urgency=critical" if urgency == "critical" else "--urgency=normal"
        
        subprocess.run(
            ["notify-send", urgency_flag, "Productivity Guard", message],
            capture_output=True,
            text=True,
            check=True
        )
    
    def _send_windows_notification(self, message: str, urgency: str) -> None:
        """Send notification on Windows."""
        # Windows 10+ toast notifications
        from win10toast import ToastNotifier
        toaster = ToastNotifier()
        toaster.show_toast(
            "Productivity Guard",
            message,
            duration=10,
            threaded=True
        )
    
    def _play_alert_sound(self) -> None:
        """Play alert sound based on platform."""
        try:
            if self.platform == "darwin":
                subprocess.run(["afplay", "/System/Library/Sounds/Glass.aiff"])
            elif self.platform.startswith("linux"):
                subprocess.run(["paplay", "/usr/share/sounds/freedesktop/stereo/bell.oga"])
            # Windows plays sound with notification
        except Exception as e:
            _LOG.debug(f"Could not play alert sound: {e}")
```

```python
# ── /src/escalator/escalator.py ─────────────────────────────────────────────────
from __future__ import annotations

import logging
import subprocess
import threading
import time
import uuid
from datetime import datetime, timedelta
from typing import Callable, Optional

from .config import EscalatorConfig
from .models import EscalationState, JustificationRecord, PromptRecord
from .notifier import Notifier

__all__ = ["Escalator"]

_LOG = logging.getLogger(__name__)

class Escalator:
    """
    Manages escalation flow and interventions.
    
    Requirement: F-7, F-8, F-9, F-10, F-11, F-12
    Version: v01
    """
    
    def __init__(
        self, 
        config: EscalatorConfig,
        on_escalation_change: Optional[Callable[[EscalationState], None]] = None
    ) -> None:
        """
        Initialize escalator.
        
        Requirement: N-2, N-3
        Version: v01
        """
        self.config = config
        self.notifier = Notifier(config.notification_sound)
        self.state = EscalationState.NORMAL
        self.on_escalation_change = on_escalation_change
        
        self.active_prompts: list[PromptRecord] = []
        self.justifications: list[JustificationRecord] = []
        self.unanswered_count = 0
        
        self._escalation_thread: Optional[threading.Thread] = None
        self._stop_escalation = threading.Event()
    
    def prompt_user(
        self, 
        task_title: str, 
        screenshot_path: str,
        custom_message: Optional[str] = None
    ) -> PromptRecord:
        """
        Prompt user for task switch justification.
        
        Requirement: F-7
        Version: v01
        """
        message = custom_message or "It looks like you've switched tasks. Why?"
        
        prompt = PromptRecord(
            id=str(uuid.uuid4()),
            message=message,
            task_title=task_title,
            screenshot_path=screenshot_path,
            prompted_at=datetime.now(),
            response_deadline=datetime.now() + timedelta(
                seconds=self.config.justification_timeout
            )
        )
        
        self.active_prompts.append(prompt)
        self.state = EscalationState.PROMPTED
        
        # Send notification
        self.notifier.send_prompt(
            f"{message}\nCurrent task: {task_title}",
            urgency="normal"
        )
        
        # Start timeout monitoring
        self._start_timeout_monitor(prompt)
        
        _LOG.info(f"Prompted user for justification (task: {task_title})")
        
        return prompt
    
    def handle_justification(
        self, 
        prompt_id: str, 
        justification_text: str
    ) -> Optional[JustificationRecord]:
        """
        Process user justification.
        
        Requirement: F-8
        Version: v01
        """
        # Find prompt
        prompt = None
        for p in self.active_prompts:
            if p.id == prompt_id:
                prompt = p
                break
        
        if not prompt:
            _LOG.warning(f"No active prompt found with ID {prompt_id}")
            return None
        
        # Mark prompt as answered
        prompt.response_received = True
        prompt.response_text = justification_text
        prompt.response_at = datetime.now()
        
        # Validate justification
        is_valid, reason = self._validate_justification(justification_text)
        
        justification = JustificationRecord(
            prompt_id=prompt_id,
            justification_text=justification_text,
            is_valid=is_valid,
            validation_reason=reason,
            submitted_at=datetime.now(),
            task_resumed=not is_valid
        )
        
        self.justifications.append(justification)
        
        # Reset state if valid
        if is_valid:
            self.state = EscalationState.NORMAL
            self.unanswered_count = 0
            self._stop_escalation.set()
            _LOG.info("Valid justification received, returning to normal mode")
        else:
            _LOG.warning(f"Invalid justification: {reason}")
        
        return justification
    
    def check_timeout(self, prompt: PromptRecord) -> bool:
        """
        Check if prompt has timed out.
        
        Requirement: F-9
        Version: v01
        """
        if prompt.response_received:
            return False
        
        return datetime.now() > prompt.response_deadline
    
    def enter_escalation_mode(self) -> None:
        """
        Enter escalation mode with increased monitoring.
        
        Requirement: F-9, F-10
        Version: v01
        """
        if self.state == EscalationState.ESCALATED:
            return
        
        self.state = EscalationState.ESCALATED
        self.unanswered_count += 1
        
        _LOG.warning(
            f"Entering escalation mode (unanswered prompts: {self.unanswered_count})"
        )
        
        # Notify state change
        if self.on_escalation_change:
            self.on_escalation_change(self.state)
        
        # Start escalation notifications
        self._start_escalation_notifications()
    
    def send_notification(self, message: str, urgency: str = "critical") -> None:
        """
        Send escalated notification.
        
        Requirement: F-11
        Version: v01
        """
        self.notifier.send_prompt(message, urgency)
    
    def execute_intervention(self) -> bool:
        """
        Execute intervention (close active tab).
        
        Requirement: F-12
        Version: v01
        """
        if not self.config.intervention_enabled:
            _LOG.info("Intervention disabled in config")
            return False
        
        if self.unanswered_count < self.config.max_unanswered_prompts:
            _LOG.info(
                f"Not enough unanswered prompts for intervention "
                f"({self.unanswered_count}/{self.config.max_unanswered_prompts})"
            )
            return False
        
        self.state = EscalationState.INTERVENING
        
        try:
            # Execute cmd+W on macOS
            if sys.platform == "darwin":
                subprocess.run([
                    "osascript", "-e",
                    'tell application "System Events" to keystroke "w" using command down'
                ])
                
                self.send_notification(
                    "Tab closed. Please return to your priority task.",
                    urgency="critical"
                )
                
                _LOG.warning("Executed intervention: closed active tab")
                return True
            
            else:
                _LOG.warning(f"Intervention not implemented for platform: {sys.platform}")
                return False
                
        except Exception as e:
            _LOG.error(f"Failed to execute intervention: {e}")
            return False
    
    def reset_escalation(self) -> None:
        """Reset escalation state."""
        self.state = EscalationState.NORMAL
        self.unanswered_count = 0
        self._stop_escalation.set()
        self.active_prompts.clear()
        
        if self.on_escalation_change:
            self.on_escalation_change(self.state)
        
        _LOG.info("Escalation state reset")
    
    def _validate_justification(self, text: str) -> tuple[bool, str]:
        """
        Validate justification text.
        
        Requirement: F-8
        Version: v01
        """
        # Simple validation rules
        # In production: could use LLM for smarter validation
        
        text = text.strip()
        
        if not text:
            return False, "Empty justification"
        
        if len(text) < 10:
            return False, "Justification too short"
        
        # Check for common invalid patterns
        invalid_patterns = [
            "asdf", "test", "xxx", "...", "idk", "whatever"
        ]
        
        if any(pattern in text.lower() for pattern in invalid_patterns):
            return False, "Invalid justification pattern"
        
        # Check for keywords indicating legitimate reasons
        valid_keywords = [
            "urgent", "emergency", "bug", "critical", "meeting",
            "blocked", "waiting", "help", "question", "issue"
        ]
        
        if any(keyword in text.lower() for keyword in valid_keywords):
            return True, "Contains valid justification keywords"
        
        # Default: accept if reasonable length
        if len(text) >= 20:
            return True, "Sufficient justification provided"
        
        return False, "Justification not convincing"
    
    def _start_timeout_monitor(self, prompt: PromptRecord) -> None:
        """Start monitoring for prompt timeout."""
        def monitor():
            time.sleep(self.config.justification_timeout)
            
            if not prompt.response_received:
                _LOG.warning(f"Prompt {prompt.id} timed out")
                self.enter_escalation_mode()
        
        thread = threading.Thread(
            target=monitor,
            name=f"TimeoutMonitor-{prompt.id}",
            daemon=True
        )
        thread.start()
    
    def _start_escalation_notifications(self) -> None:
        """Start sending escalation notifications."""
        if self._escalation_thread and self._escalation_thread.is_alive():
            return
        
        self._stop_escalation.clear()
        
        def notify_loop():
            notification_count = 0
            
            while not self._stop_escalation.is_set():
                notification_count += 1
                
                message = (
                    f"ATTENTION: You have {self.unanswered_count} unanswered prompts. "
                    f"Please return to your priority task!"
                )
                
                self.send_notification(message, urgency="critical")
                
                # Check if we should intervene
                if (notification_count % 3 == 0 and 
                    self.unanswered_count >= self.config.max_unanswered_prompts):
                    self.execute_intervention()
                
                # Wait for next notification
                self._stop_escalation.wait(self.config.notification_interval)
        
        self._escalation_thread = threading.Thread(
            target=notify_loop,
            name="EscalationNotifications",
            daemon=True
        )
        self._escalation_thread.start()
```

## API Specification

### Escalator Class

**Methods:**
- `prompt_user(task_title: str, screenshot_path: str, custom_message: Optional[str]) -> PromptRecord` - Send justification prompt
- `handle_justification(prompt_id: str, justification_text: str) -> Optional[JustificationRecord]` - Process user response
- `check_timeout(prompt: PromptRecord) -> bool` - Check if prompt timed out
- `enter_escalation_mode() -> None` - Begin escalated monitoring
- `execute_intervention() -> bool` - Close active tab
- `reset_escalation() -> None` - Return to normal state

### EscalationState Enum

**Values:**
- `NORMAL` - No active prompts
- `PROMPTED` - Waiting for justification
- `ESCALATED` - In escalation mode
- `INTERVENING` - Executing interventions

## Example Usage

```python
from src.escalator import Escalator, EscalatorConfig

# Initialize
config = EscalatorConfig(
    justification_timeout=60,
    notification_interval=10,
    max_unanswered_prompts=3
)

def on_state_change(new_state):
    print(f"Escalation state changed to: {new_state.value}")

escalator = Escalator(config, on_escalation_change=on_state_change)

# Prompt user when misalignment detected
prompt = escalator.prompt_user(
    task_title="Complete PRD documentation",
    screenshot_path="/path/to/screenshot.png"
)

# Handle user response
justification = escalator.handle_justification(
    prompt_id=prompt.id,
    justification_text="Had to check urgent production issue"
)

if justification and justification.is_valid:
    print("Valid justification, continuing normal operation")
else:
    print("Invalid justification, remaining in escalation")
```

## Version History

- **v01** (2025-06-15): Initial implementation
  - Multi-platform notification support
  - Timeout monitoring with escalation
  - Progressive intervention system
  - Basic justification validation